import { supabase } from '../config/db.js';
import { AppError } from '../middleware/errorHandler.js';

class TagService {
  async getTagsByType(type) {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('id, name, status')
        .eq('type', type)
        .eq('flag_deleted', false)
        .order('name');

      if (error) throw error;
      return data;
    } catch (error) {
      throw new AppError(`Error fetching ${type} tags: ${error.message}`, 500);
    }
  }

  async getAllTagsGroupedByType() {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('type, id, name, status')
        .eq('flag_deleted', false)
        .order('type, name');

      if (error) throw error;

      // Group the data by type
      const groupedTags = data.reduce((acc, tag) => {
        if (!acc[tag.type]) {
          acc[tag.type] = [];
        }
        acc[tag.type].push({
          id: tag.id,
          name: tag.name,
          status: tag.status
        });
        return acc;
      }, {});

      // Convert to array format
      return Object.entries(groupedTags).map(([type, tags]) => ({
        type,
        tags
      }));
    } catch (error) {
      throw new AppError('Error fetching grouped tags: ' + error.message, 500);
    }
  }

  async getTagsByIds(tagIds) {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('id, name, type, status')
        .in('id', tagIds)
        .eq('flag_deleted', false)
        .order('name');

      if (error) throw error;
      return data;
    } catch (error) {
      throw new AppError(`Error fetching tags by IDs: ${error.message}`, 500);
    }
  }

  async createTag(name, type) {
    try {
      const { data, error } = await supabase
        .from('tags')
        .insert([{
          name,
          type,
          status: true
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new AppError(`Error creating tag: ${error.message}`, 500);
    }
  }

  async updateTag(id, { name, type, status }) {
    try {
      const { data, error } = await supabase
        .from('tags')
        .update({
          name,
          type,
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new AppError(`Error updating tag: ${error.message}`, 500);
    }
  }

  async deleteTag(id) {
    try {
      const { error } = await supabase
        .from('tags')
        .update({
          flag_deleted: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      throw new AppError(`Error deleting tag: ${error.message}`, 500);
    }
  }

  async getTagById(id) {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('id, name, type, status')
        .eq('id', id)
        .eq('flag_deleted', false)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new AppError(`Error fetching tag: ${error.message}`, 500);
    }
  }
}

export default new TagService();