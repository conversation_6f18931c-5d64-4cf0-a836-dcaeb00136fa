import { useState, useEffect } from 'react';

interface Story {
  id: string;
  title: string;
  oneLine: string;
  clientSegment: string;
  industries: string[];
  regions: string[];
  technologies: string[];
  has_pdf: boolean;
  has_video: boolean;
  has_audio: boolean;
  has_image: boolean;
}

interface Filters {
  industries?: string[];
  regions?: string[];
  technologies?: string[];
  outcomes?: string[];
  tags?: string[];
  title?: string;
}

export const usePublishedStories = (filters?: Filters) => {
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStories = async () => {
      try {
        const token = localStorage.getItem('winwise_token');
        // console.log('Fetching stories from:', `${import.meta.env.VITE_API_URL}/api/stories?status=published`);
        const response = await fetch(`${import.meta.env.VITE_API_URL}/api/stories?status=published`, {
          headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` })
          }
        });
        // console.log('API Response:', response);

        if (!response.ok) {
          console.error('Error fetching published stories, status:', response.status);
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || `Failed to fetch published stories with status ${response.status}`);
        }
        
        const data = await response.json();
        
        // Transform the data to match the frontend structure with proper names
        const transformedStories = data.data.rows?.map((story: any) => ({
          id: story.id,
          title: story.title || '',
          oneLine: story.oneLine || story.oneline_summary || '',
          clientSegment: story.clientSegment || story.client_segment || '',
          industries: Array.isArray(story.industries) 
            ? story.industries 
            : story.industries 
              ? JSON.parse(story.industries) 
              : [],
          regions: Array.isArray(story.regions) 
            ? story.regions 
            : story.regions 
              ? JSON.parse(story.regions) 
              : [],
          technologies: Array.isArray(story.technologies) 
            ? story.technologies 
            : story.technologies 
              ? JSON.parse(story.technologies) 
              : [],
          has_pdf: Boolean(story.has_pdf),
          has_video: Boolean(story.has_video),
          has_audio: Boolean(story.has_audio),
          has_image: Boolean(story.has_image)
        })) || [];
        
        setStories(transformedStories);
      } catch (err) {
        console.error('Error fetching stories:', err);
        setError('Failed to fetch stories');
      } finally {
        setLoading(false);
      }
    };

    fetchStories();
  }, [filters]); // Re-fetch when filters change

  return { stories, loading, error };
};