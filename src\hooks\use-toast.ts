
import * as React from "react";
import { toast as sonnerToast } from "sonner";

const TOAST_LIMIT = 1;
export type ToasterToast = {
  id: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: React.ReactNode;
  variant?: "default" | "destructive";
};

const toastStore = {
  toasts: [] as ToasterToast[],
  listeners: new Set<() => void>(),
  subscribe(listener: () => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  },
  notify() {
    this.listeners.forEach((listener) => listener());
  },
};

export function toast({
  ...props
}: {
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: React.ReactNode;
  variant?: "default" | "destructive";
}) {
  return sonnerToast(props.title as string, {
    description: props.description,
    action: props.action,
    className: props.variant === "destructive" ? "bg-destructive text-destructive-foreground" : undefined,
  });
}

export function useToast() {
  const [toasts, setToasts] = React.useState<ToasterToast[]>([]);

  React.useEffect(() => {
    return toastStore.subscribe(() => {
      setToasts([...toastStore.toasts]);
    });
  }, []);

  return {
    toast,
    toasts,
    dismiss: (toastId?: string) => {
      if (toastId) {
        sonnerToast.dismiss(toastId);
      }
    },
  };
}
