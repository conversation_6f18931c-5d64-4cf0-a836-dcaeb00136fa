import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight, Loader2 } from "lucide-react";
import { useStory } from "./StoryContext";
import { useStoryApi } from "@/hooks/useStoryApi";
import { toast } from "@/components/ui/use-toast";

interface BasicInformationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  onComplete: () => void;
  iseditMode?: boolean;
  editStoryData?: any;
}

export const BasicInformation: React.FC<BasicInformationProps> = ({
  setActiveTab,
  onComplete,
  iseditMode = false,
  editStoryData,
}) => {
  const {
    storyData,
    handleInputChange,
    documentResponse,
    setStoryResponse,
    setPublishedPayload,
  } = useStory();

  const { createStory, updateStory } = useStoryApi();
  const [isProcessing, setIsProcessing] = useState(false);

  const basePayload = {
    client_segment: storyData.clientSegment || "",
    oneline_summary: storyData.oneLine || "",
    business_challenges: storyData.businessChallenges || "",
    executive_summary: storyData.summary || "",
    success_story_detail: storyData.successStory || "",
  };

  const handleNextStep = async () => {
    setIsProcessing(true);
    try {
      const payload = iseditMode
        ? { ...basePayload, status: editStoryData.status }
        : { ...basePayload, title: storyData.title, status: "draft" };

        console.log("payload", payload);

      const response = iseditMode
        ? await updateStory(storyData.id, payload)
        : await createStory(payload);

      if (!response) throw new Error("Failed to save story");

      setPublishedPayload(payload);
      setStoryResponse(response);
      onComplete();
      setActiveTab("category");
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to save story. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    const info =
      documentResponse?.n8nResponse?.message?.content?.BasicInformation;
    if (!info) return;

    if (!iseditMode) handleInputChange("title", info.Title || "");

    handleInputChange("clientSegment", info.ClientSegment || "");
    handleInputChange("oneLine", info.OneLine_Summary || "");
    handleInputChange("summary", info.ExecutiveSummary || "");
    handleInputChange("businessChallenges", info.BusinessChallenges || "");
    handleInputChange("successStory", info.SuccessStoryDetail || "");
  }, [documentResponse]);

  const renderInput = (
    id: string,
    label: string,
    placeholder: string,
    value: string,
    onChange: (val: string) => void,
    isTextarea = false,
    helperText?: string
  ) => (
    <div>
      <Label htmlFor={id} className="text-base font-medium">
        {label} <span className="text-red-500">*</span>
      </Label>
      {isTextarea ? (
        <Textarea
          id={id}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="mt-1.5 min-h-[100px]"
        />
      ) : (
        <Input
          id={id}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="mt-1.5"
        />
      )}
      {helperText && (
        <p className="text-xs text-muted-foreground mt-1">{helperText}</p>
      )}
    </div>
  );

  return (
    <Card className="border-mobio-lavender/40">
      <CardHeader className="bg-mobio-lavender/10">
        <CardTitle className="text-mobio-blue">Basic Information</CardTitle>
        <CardDescription>
          Enter the core details of your success story
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4 pt-5">
        {renderInput(
          "title",
          "Title",
          "Enter a descriptive title",
          storyData.title,
          (val) => handleInputChange("title", val)
        )}
        {renderInput(
          "clientSegment",
          "Client Segment",
          "Enter client segment (e.g., Enterprise, SMB, Government)",
          storyData.clientSegment || "",
          (val) => handleInputChange("clientSegment", val)
        )}
        {renderInput(
          "oneLine",
          "1-Line Summary",
          "Brief one-line overview of the success story",
          storyData.oneLine || "",
          (val) => handleInputChange("oneLine", val),
          false,
          "A concise description that captures the essence of the success story"
        )}
        {renderInput(
          "businessChallenges",
          "Business Challenges",
          "Describe key business challenges addressed",
          storyData.businessChallenges || "",
          (val) => handleInputChange("businessChallenges", val),
          true
        )}
        {renderInput(
          "summary",
          "Executive Summary",
          "Brief overview of the success story",
          storyData.summary || "",
          (val) => handleInputChange("summary", val),
          true
        )}
        {renderInput(
          "successStory",
          "Success Story",
          "Provide a detailed narrative of the success story",
          storyData.successStory || "",
          (val) => handleInputChange("successStory", val),
          true,
          "Include detailed information about the implementation, outcomes, and impact achieved"
        )}

        <div className="flex justify-end pt-4">
          <Button
            type="button"
            className="bg-mobio-blue text-white hover:bg-mobio-blue/90"
            onClick={handleNextStep}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                Next Step
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
