import { AppError } from './errorHandler.js';

// Define a factory function to create role-based middleware
export const requireRole = (requiredRole) => {
  return (req, _res, next) => {
    if (!req.user) {
      throw new AppError('Authentication required', 401);
    }

    if (req.user.role !== requiredRole) {
      throw new AppError(`${requiredRole.charAt(0).toUpperCase() + requiredRole.slice(1)} access required`, 403);
    }

    next();
  };
};

// Create specific role middleware instances
export const requireAdmin = requireRole('admin');
export const requireUser = requireRole('user');