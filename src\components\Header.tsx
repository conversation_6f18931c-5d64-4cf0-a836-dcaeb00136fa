
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Menu } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";

const Header = () => {
  const { logout } = useAuth();
  const [isListening, setIsListening] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const handleVoiceSearch = () => {
    if (!('webkitSpeechRecognition' in window)) {
      toast({
        title: "Voice Search Unavailable",
        description: "Your browser doesn't support voice recognition.",
        variant: "destructive",
      });
      return;
    }
    
    setIsListening(true);
    
    // This is a mockup - in a real app, you'd use the Web Speech API
    setTimeout(() => {
      setSearchQuery("Show me success stories from FinTech in Australia");
      setIsListening(false);
      
      toast({
        title: "Voice Search Completed",
        description: "Searching for: FinTech success stories in Australia",
      });
    }, 2000);
  };

  return (
    <header className="sticky top-0 z-10 w-full h-16 border-b bg-white flex items-center px-2 sm:px-4">
      <div className="block md:hidden">
        <SidebarTrigger>
          <Button variant="ghost" size="icon" className="hover-transition btn-hover">
            <Menu className="h-5 w-5" />
          </Button>
        </SidebarTrigger>
      </div>

      <div className="flex-1"></div>

      <div className="flex items-center ml-2 sm:ml-4 space-x-1 sm:space-x-2">
        <Button variant="ghost" size="icon" className="relative hover-transition hover:bg-secondary/80">
          <Bell className="h-5 w-5" />
          <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full animate-pulse-soft" />
        </Button>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-8 w-8 rounded-full hover:bg-secondary/80 hover-transition hover:scale-110">
              <Avatar className="h-8 w-8 hover-glow">
                <AvatarImage src="https://github.com/shadcn.png" alt="User" />
                <AvatarFallback>MS</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 animate-scale-in" align="end" forceMount>
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">Sarah Johnson</p>
                <p className="text-xs leading-none text-muted-foreground"><EMAIL></p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="hover-transition cursor-pointer">Profile</DropdownMenuItem>
            <DropdownMenuItem className="hover-transition cursor-pointer">Settings</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="hover-transition cursor-pointer" onClick={logout}>Log out</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default Header;
