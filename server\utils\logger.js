import winston from "winston";
import util from "util";

// Define custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ level, message, timestamp, ...metadata }) => {
    let log = `${timestamp} ${level.toUpperCase()}: `;

    if (typeof message === 'object') {
      log += util.inspect(message, { depth: null, colors: false });
    } else {
      log += message;
    }

    if (Object.keys(metadata).length) {
      log += `\nMetadata: ${util.inspect(metadata, { depth: null, colors: false })}`;
    }

    return log;
  })
);

// Create the logger instance
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || "info",
  format: logFormat,
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        logFormat
      ),
    }),
    // File transport for production
    new winston.transports.File({
      filename: "logs/error.log",
      level: "error",
    }),
    new winston.transports.File({
      filename: "logs/combined.log",
    }),
  ],
});

// Add request context if needed
export const addRequestContext = (req) => ({
  requestId: req.id,
  method: req.method,
  url: req.url,
  ip: req.ip,
});

// Helper function to stringify objects
export const stringifyObject = (obj) => 
  util.inspect(obj, { depth: null, colors: false });

// Wrap logger methods to handle objects and add metadata
const wrapLoggerMethod = (method) => (message, metadata = {}) => {
  if (typeof message === 'object') {
    metadata = { ...metadata, ...message };
    message = 'Object logged:';
  }
  logger[method](message, metadata);
};

// Export wrapped logger methods
export const log = {
  info: wrapLoggerMethod('info'),
  error: wrapLoggerMethod('error'),
  warn: wrapLoggerMethod('warn'),
  debug: wrapLoggerMethod('debug'),
};

export default log;