
import tagService from '../services/tag.service.js';

class TagController {
  async getTagsByType(req, res, next) {
    try {
      const { type } = req.params;
      
      // Validate tag type
      const validTypes = ['industry', 'region', 'technology', 'outcome', 'business_problem', 'general_tag'];
      if (!validTypes.includes(type)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid tag type',
          validTypes
        });
      }

      const tags = await tagService.getTagsByType(type);
      return res.status(200).json({
        success: true,
        data: tags
      });
    } catch (error) {
      next(error);
    }
  }

  async getAllTagsGrouped(req, res, next) {
    try {
      const groupedTags = await tagService.getAllTagsGroupedByType();
      return res.status(200).json({
        success: true,
        data: groupedTags
      });
    } catch (error) {
      next(error);
    }
  }

  async getTagsByIds(req, res, next) {
    try {
      const { ids } = req.body; // Expecting an array of tag IDs in the request body
      
      // Validate that ids is an array
      if (!Array.isArray(ids)) {
        return res.status(400).json({
          success: false,
          message: 'IDs must be provided as an array'
        });
      }

      const tags = await tagService.getTagsByIds(ids);
      return res.status(200).json({
        success: true,
        data: tags
      });
    } catch (error) {
      next(error);
    }
  }

  async createTag(req, res, next) {
    try {
      const { name, type } = req.body;
      
      // Validate required fields
      if (!name || !type) {
        return res.status(400).json({
          success: false,
          message: 'Name and type are required'
        });
      }

      const tag = await tagService.createTag(name, type);
      return res.status(201).json({
        success: true,
        data: tag
      });
    } catch (error) {
      next(error);
    }
  }

  async updateTag(req, res, next) {
    try {
      const { id } = req.params;
      const { name, type, status } = req.body;
      
      const tag = await tagService.updateTag(id, { name, type, status });
      return res.status(200).json({
        success: true,
        data: tag
      });
    } catch (error) {
      next(error);
    }
  }

  async deleteTag(req, res, next) {
    try {
      const { id } = req.params;
      await tagService.deleteTag(id);
      return res.status(200).json({
        success: true,
        message: 'Tag deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  async getTagById(req, res, next) {
    try {
      const { id } = req.params;
      const tag = await tagService.getTagById(id);
      
      if (!tag) {
        return res.status(404).json({
          success: false,
          message: 'Tag not found'
        });
      }

      return res.status(200).json({
        success: true,
        data: tag
      });
    } catch (error) {
      next(error);
    }
  }
}

export default new TagController();