import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Bookmark, Share2 } from "lucide-react";

interface Tag {
  id: string;
  name: string;
}

interface Story {
  confidential?: boolean;
  title: string;
  clientSegment: string;
  publishDate: string; // Or Date
  oneLineSummary: string;
}

interface StoryDetailHeaderProps {
  story: Story;
  industryTags: Tag[];
  isSaved: boolean;
  onSaveToLibrary: () => void;
  onShare: () => void;
}

const StoryDetailHeader = ({
  story,
  industryTags,
  isSaved,
  onSaveToLibrary,
  onShare,
}: StoryDetailHeaderProps) => {
  return (
    <div className="bg-gradient-to-r from-mobio-blue/10 to-mobio-lavender/20 rounded-xl p-6 mb-8 shadow-sm">
      <div className="flex flex-col md:flex-row justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            {story.confidential && (
              <Badge variant="destructive" className="ml-2">
                Confidential
              </Badge>
            )}
          </div>

          <h1 className="text-2xl md:text-3xl font-semibold text-mobio-blue mb-3">
            {story.title}
          </h1>

          <div className="flex items-center text-muted-foreground mb-4">
            <Badge variant="secondary" className="mr-4">
              {story.clientSegment}
            </Badge>

            <Calendar className="h-4 w-4 mr-1 ml-2" />
            <span>{new Date(story.publishDate).toLocaleDateString()}</span>
          </div>

          <p className="text-gray-600 mb-4 max-w-3xl">
            {story.oneLineSummary}
          </p>

          <div className="flex flex-wrap gap-2 mt-4">
            {industryTags.map((tag) => (
              <Badge key={tag.id} variant="outline" className="bg-blue-50">
                {tag.name}
              </Badge>
            ))}
          </div>
        </div>

        <div className="flex md:flex-col gap-2 mt-4 md:mt-0 md:ml-4">
          <Button
            onClick={onSaveToLibrary}
            variant="outline"
            className={`${
              isSaved
                ? "bg-mobio-blue/10 border-mobio-blue text-mobio-blue"
                : ""
            }`}
          >
            <Bookmark
              className={`h-4 w-4 mr-2 ${isSaved ? "fill-mobio-blue" : ""}`}
            />
            {isSaved ? "Saved" : "Save"}
          </Button>

          <Button variant="outline" onClick={onShare}>
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>
      </div>
    </div>
  );
};

export default StoryDetailHeader;