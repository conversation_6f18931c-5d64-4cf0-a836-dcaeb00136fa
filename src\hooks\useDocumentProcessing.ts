import { useState } from 'react';
import { toast } from 'react-toastify';

interface UseDocumentProcessingProps {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

export const useDocumentProcessing = ({ onSuccess, onError }: UseDocumentProcessingProps = {}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const processDocument = async (file: File | null, url?: string, description?: string) => {
    setIsProcessing(true);
    try {
      const formData = new FormData();
      
      if (file) {
        formData.append('document', file);
      }
      if (url) {
        formData.append('url', url);
      }
      if (description) {
        formData.append('description', description);
      }     // Por ejemplo: const apiUrl = 'sole.log('FormData:
      const apiUrl = import.meta.env.VITE_API_URL;
      const token = localStorage.getItem('winwise_token');

      if (!token) {
        toast.error("Authentication token not found. Please log in.");
        setIsProcessing(false);
        const authError = new Error("Authentication token not found.");
        onError?.(authError);
        throw authError;
      }
     
      const response = await fetch(`${apiUrl}/api/documents/process`, {
        method: 'POST',
        headers: {
          // 'Content-Type': 'multipart/form-data' is automatically set by browser for FormData
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

    
      if (!response.ok) {
        const errorData = await response.json().catch(() => {
          console.error('Failed to parse error response');
          return {};
        });
        console.error('Error response data:', errorData);
        throw new Error(errorData.message || 'Failed to process document');
      }

      const data = await response.json();
      onSuccess?.(data);
      return data;
    } catch (error) {
      console.error('Processing error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while processing the document';
      toast.error(errorMessage);
      onError?.(error as Error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    isProcessing,
    processDocument
  };
};