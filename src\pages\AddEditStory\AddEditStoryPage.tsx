import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { StoryProvider, useStory } from "./StoryContext";
import { AiContentGenerator } from "./AiContentGenerator";
import { BasicInformation } from "./BasicInformation";
import { Categorization } from "./Categorization";
import { Attachments } from "./Attachments";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useState } from "react";
import { SidebarComponents } from "./Sidebar";
import { toast } from "@/components/ui/use-toast";

const TAB_CONFIG = [
  { value: "basic", label: "Basic Information" },
  { value: "category", label: "Categorization" },
  { value: "attachments", label: "Attachments" },
];

const AddEditStoryContent = () => {
  const navigate = useNavigate();
  const { saveStory, isSaving } = useStory();
  const { id } = useParams();
  const isEditMode = !!id;
  const [activeTab, setActiveTab] = useState("basic");
  const [isBasicInfoCompleted, setIsBasicInfoCompleted] = useState(false);

  // 1. Local loading state for Save Draft
  const [isSavingDraft, setIsSavingDraft] = useState(false);

  const handleCancel = () => navigate("/admin/repository");

  const handleSave = async () => {
    setIsSavingDraft(true);
    try {
      await saveStory("draft");
      toast({ title: "Success", description: "Story saved as draft" });
      navigate("/admin/repository");
    } catch (error) {
      toast({ title: "Error", description: "Failed to save draft", variant: "destructive" });
    } finally {
      setIsSavingDraft(false);
    }
  };

  const handlePublish = async () => {
    try {
      await saveStory("published");
      toast({ title: "Success", description: "Story published successfully" });
      navigate("/admin/repository");
    } catch (error) {
      toast({ title: "Error", description: "Failed to publish story", variant: "destructive" });
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold text-mobio-blue">
        {isEditMode ? "Edit Success Story" : "Add New Success Story"}
      </h1>
      
      <AiContentGenerator />
      
      <div className="grid gap-6 md:grid-cols-3">
        <div className="col-span-2 space-y-6">
          <Tabs 
            value={activeTab} 
            onValueChange={(value) => {
              if (isBasicInfoCompleted || value === "basic") {
                setActiveTab(value);
              }
            }}
          >
            <TabsList className="w-full bg-slate-100 p-0 h-auto mb-6">
              {TAB_CONFIG.map(({ value, label }) => (
                <TabsTrigger 
                  key={value}
                  value={value}
                  className="flex-1 py-3 data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-mobio-blue"
                  disabled={!isBasicInfoCompleted && value !== "basic"}
                >
                  {label}
                </TabsTrigger>
              ))}
            </TabsList>
            
            {TAB_CONFIG.map(({ value }) => (
              <TabsContent key={value} value={value} className="mt-0">
                {value === "basic" && (
                  <BasicInformation 
                    activeTab={activeTab} 
                    setActiveTab={setActiveTab}
                    onComplete={() => {
                      setIsBasicInfoCompleted(true);
                      setActiveTab('category');
                    }}
                  />
                )}
                {value === "category" && <Categorization setActiveTab={setActiveTab} />}
                {value === "attachments" && <Attachments />}
              </TabsContent>
            ))}
          </Tabs>
        </div>
        
        <div className="col-span-1 space-y-6">
          <SidebarComponents />
        </div>
      </div>
      
      <div className="mt-8 flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={handleCancel}>
          Cancel
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={handleSave}
          disabled={!isBasicInfoCompleted || isSavingDraft}
        >
          {isSavingDraft ? (
            <>
              <span className="animate-spin mr-2">⏳</span> Saving...
            </>
          ) : (
            "Save Draft"
          )}
        </Button>
        <Button
          type="button"
          onClick={handlePublish}
          className="bg-mobio-lavender text-foreground hover:bg-mobio-lavender/90"
          disabled={!isBasicInfoCompleted || isSaving}
        >
          {isEditMode ? "Update & Publish" : "Publish Story"}
        </Button>
      </div>
    </div>
  );
};

const AddEditStoryPage = () => {
  const { id } = useParams();
  return (
    <StoryProvider editId={id}>
      <AddEditStoryContent />
    </StoryProvider>
  );
};

export default AddEditStoryPage;