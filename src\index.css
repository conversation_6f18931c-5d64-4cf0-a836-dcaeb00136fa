
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 211 100% 42%;
    --primary-foreground: 210 40% 98%;

    --secondary: 220 13% 95%;
    --secondary-foreground: 222 47% 11%;

    --muted: 220 13% 91%;
    --muted-foreground: 215 16% 47%;

    --accent: 240 73% 97%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 211 100% 42%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222 47% 11%;
    --sidebar-primary: 211 100% 42%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 13% 95%;
    --sidebar-accent-foreground: 222 47% 11%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 211 100% 42%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  .glass-card {
    @apply bg-white/60 backdrop-blur-sm border border-white/20 shadow-md rounded-lg;
  }

  .pill-button {
    @apply rounded-full px-4 py-1.5 text-sm font-medium transition-colors;
  }
  
  .fade-in {
    @apply animate-fade-in;
  }
  
  .slide-in {
    @apply animate-slide-in;
  }

  /* Interactive card effect */
  .interactive-card {
    @apply transition-all duration-300 hover:shadow-lg hover:scale-[1.02] hover:ring-2 hover:ring-primary/30;
  }
  
  /* Shimmer effect for loading states */
  .shimmer {
    @apply bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:200%_100%] animate-shimmer;
  }
  
  /* Float animation for attention */
  .float {
    @apply animate-float;
  }
  
  /* Smooth transition for all hover effects */
  .hover-transition {
    @apply transition-all duration-200 ease-in-out;
  }
  
  /* Button hover effect */
  .btn-hover {
    @apply hover:shadow-md hover:translate-y-[-2px] active:translate-y-0 active:shadow-sm transition-all duration-200;
  }
  
  /* Active navigation item */
  .nav-item-active {
    @apply bg-primary/10 text-primary font-medium;
  }
  
  /* Hover glow effect */
  .hover-glow {
    @apply hover:shadow-[0_0_15px_rgba(59,130,246,0.5)] transition-shadow duration-300;
  }
}
