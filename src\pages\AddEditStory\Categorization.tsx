import React, { useEffect, useMemo, useCallback, useState } from "react";
import { useStory } from "./StoryContext";
import { useGroupedTags } from "@/hooks/useGroupedTags";
import { useStoryApi } from "@/hooks/useStoryApi";
import { useMediaUpload } from "@/hooks/useMediaUpload";
import { toast } from "@/components/ui/use-toast";
import { Story } from "./types";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X, Plus, Loader2, ArrowRight } from "lucide-react";

type CategoryType = keyof Story["categories"];

interface CategorizationProps {
  setActiveTab: (tab: string) => void;
}

interface CategoryBlockProps {
  label: string;
  type: CategoryType;
  values: string[];
  options: string[];
  onAdd: (type: CategoryType, value: string) => void;
  onRemove: (type: CategoryType, value: string) => void;
  badgeClass: string;
}

const CategoryBlock: React.FC<CategoryBlockProps> = React.memo(({
  label,
  type,
  values,
  options,
  onAdd,
  onRemove,
  badgeClass,
}) => (
  <div>
    <Label className="text-base font-medium mb-2 block">{label}</Label>
    <div className="flex flex-wrap gap-2 mb-2">
      {values.map((item) => (
        <Badge key={item} className={badgeClass}>
          {item}
          <button
            className="ml-1 hover:text-red-500"
            onClick={() => onRemove(type, item)}
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      ))}
    </div>
    <ScrollArea className="h-32 w-full border rounded-md p-2">
      <div className="flex flex-wrap gap-1">
        {options.map((option) => (
          <Button
            key={option}
            variant="outline"
            size="sm"
            className="text-xs"
            onClick={() => onAdd(type, option)}
            disabled={values.includes(option)}
          >
            <Plus className="mr-1 h-3 w-3" /> {option}
          </Button>
        ))}
      </div>
    </ScrollArea>
  </div>
));

CategoryBlock.displayName = "CategoryBlock";

export const Categorization: React.FC<CategorizationProps> = ({ setActiveTab }) => {
  const { storyData, handleCategoryChange, documentResponse, storyResponse, setStoryResponse } = useStory();
  const { data: tagGroups, isLoading, error } = useGroupedTags();
  const { updateStory } = useStoryApi();
  const { createRequest } = useMediaUpload();
  const [isProcessing, setIsProcessing] = useState(false);
  const [localCategories, setLocalCategories] = useState<Story["categories"]>(storyData.categories);

  const getTagsByType = useCallback((type: string): string[] => {
    const group = tagGroups?.find((g) => g.type.toLowerCase() === type.toLowerCase());
    const names = group?.tags.filter((tag) => tag.status).map((tag) => tag.name) || [];
    return Array.from(new Set(names.map((name) => name.toLowerCase())))
      .map((lowercaseName) => names.find((name) => name.toLowerCase() === lowercaseName) || lowercaseName);
  }, [tagGroups]);

  const handleAddCategory = useCallback((categoryType: CategoryType, value: string) => {
    setLocalCategories((prev) => ({
      ...prev,
      [categoryType]: [...new Set([...(prev[categoryType] || []), value])],
    }));
  }, []);

  const handleRemoveCategory = useCallback((categoryType: CategoryType, value: string) => {
    setLocalCategories((prev) => ({
      ...prev,
      [categoryType]: (prev[categoryType] || []).filter((item) => item !== value),
    }));
  }, []);

  useEffect(() => {
    if (!tagGroups || !storyData.categories) return;

    Object.entries(storyData.categories).forEach(([categoryType, categoryIds]) => {
      if (!Array.isArray(categoryIds) || categoryIds.length === 0) return;

      const tagGroup = tagGroups.find((group) =>
        group.type.toLowerCase() === (categoryType === "businessProblem" ? "business_problem" : categoryType).toLowerCase()
      );

      if (!tagGroup) return;

      const categoryNames = categoryIds
        .map((id) => tagGroup.tags.find((t) => t.id === id && t.status)?.name)
        .filter((name): name is string => !!name);

      if (categoryNames.length > 0) {
        setLocalCategories((prev) => ({
          ...prev,
          [categoryType]: categoryNames,
        }));
      }
    });
  }, [tagGroups, storyData.categories]);

  useEffect(() => {
    const categories = documentResponse?.n8nResponse?.message?.content?.Categorization;
    if (!categories) return;

    const updateCategory = (type: CategoryType, values: string | string[], optionType: string) => {
      const options = getTagsByType(optionType);
      const categoryValues = Array.isArray(values) ? values : [values];
      const validValues = categoryValues.filter((value) =>
        options.some((option) => option.toLowerCase() === value.toLowerCase())
      );
      if (validValues.length > 0) {
        setLocalCategories((prev) => ({
          ...prev,
          [type]: validValues,
        }));
      }
    };

    if (categories.Industry) updateCategory("industry", categories.Industry, "industry");
    if (categories.Region) updateCategory("region", categories.Region, "region");
    if (categories.Technologies) updateCategory("technology", categories.Technologies, "technology");
    if (categories.Outcomes) updateCategory("outcome", categories.Outcomes, "outcome");
  }, [documentResponse, getTagsByType]);

  const handleNextStep = async () => {
    if (!storyResponse?.data?.id) {
      toast({ title: "Error", description: "Story ID not found", variant: "destructive" });
      return;
    }

    try {
      setIsProcessing(true);
      const storyId = storyResponse.data.id;

      const getIdsForCategory = (type: string, values: string[]) => {
        if (!values || values.length === 0) return [];

        const tagGroup = tagGroups?.find((group) => {
          const groupType = type === "businessProblem" ? "business_problem" : type;
          return group.type.toLowerCase() === groupType.toLowerCase();
        });

        if (!tagGroup) return [];

        return values
          .map((value) => tagGroup.tags.find((tag) => tag.name.toLowerCase() === value.toLowerCase() && tag.status)?.id)
          .filter(Boolean);
      };

      const payload = {
        industries: getIdsForCategory("industry", localCategories.industry || []),
        regions: getIdsForCategory("region", localCategories.region || []),
        technologies: getIdsForCategory("technology", localCategories.technology || []),
        outcomes: getIdsForCategory("outcome", localCategories.outcome || []),
        business_problem: getIdsForCategory("businessProblem", localCategories.businessProblem || []), 
        general_tag: getIdsForCategory("general_tag", localCategories.general_tag || []),
      };

      const response = await updateStory(storyId, payload);
      setStoryResponse(response);

      if (response) {
        handleCategoryChange("industry", localCategories.industry || []);
        handleCategoryChange("technology", localCategories.technology || []);
        handleCategoryChange("businessProblem", localCategories.businessProblem || []);
        handleCategoryChange("region", localCategories.region || []);
        handleCategoryChange("outcome", localCategories.outcome || []);
        handleCategoryChange("general_tag", localCategories.general_tag || []);

        const createFolderResponse = await createRequest(storyId);
        if (createFolderResponse) {
          setActiveTab("attachments");
        } else {
          toast({
            title: "Warning",
            description: "Story updated but folder creation failed",
            variant: "destructive",
          });
        }
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
      toast({
        title: "Error",
        description: `Failed to update story categories: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const categoryBlocks = useMemo(() => [
    {
      label: "Industry",
      type: "industry" as CategoryType,
      values: localCategories.industry || [],
      options: getTagsByType("industry"),
      badgeClass: "bg-blue-100 text-blue-800 hover:bg-blue-200",
    },
    {
      label: "Technology",
      type: "technology" as CategoryType,
      values: localCategories.technology || [],
      options: getTagsByType("technology"),
      badgeClass: "bg-green-100 text-green-800 hover:bg-green-200",
    },
    {
      label: "Business Problem",
      type: "businessProblem" as CategoryType,
      values: localCategories.businessProblem || [],
      options: getTagsByType("business_problem"),
      badgeClass: "bg-purple-100 text-purple-800 hover:bg-purple-200",
    },
    {
      label: "Region",
      type: "region" as CategoryType,
      values: localCategories.region || [],
      options: getTagsByType("region"),
      badgeClass: "bg-amber-100 text-amber-800 hover:bg-amber-200",
    },
    {
      label: "Outcome",
      type: "outcome" as CategoryType,
      values: localCategories.outcome || [],
      options: getTagsByType("outcome"),
      badgeClass: "bg-red-100 text-red-800 hover:bg-red-200",
    },
    {
      label: "General Tags",
      type: "general_tag" as CategoryType,
      values: localCategories.general_tag || [],
      options: getTagsByType("general_tag"),
      badgeClass: "bg-gray-100 text-gray-800 hover:bg-gray-200",
    },
  ], [localCategories, getTagsByType]);

  if (isLoading) {
    return (
      <Card className="border-mobio-lavender/40">
        <CardHeader className="bg-mobio-lavender/10">
          <CardTitle className="text-mobio-blue">Categorization</CardTitle>
          <CardDescription>Loading categories...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center py-8">
          <Loader2 className="h-6 w-4 animate-spin text-mobio-blue" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-mobio-lavender/40">
        <CardHeader className="bg-mobio-lavender/10">
          <CardTitle className="text-mobio-blue">Categorization</CardTitle>
          <CardDescription className="text-red-500">Error loading categories</CardDescription>
        </CardHeader>
        <CardContent className="py-4">
          <p className="text-red-500">{error.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-mobio-lavender/40">
      <CardHeader className="bg-mobio-lavender/10">
        <CardTitle className="text-mobio-blue">Categorization</CardTitle>
        <CardDescription>Tag your story with relevant categories</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 pt-5">
        {categoryBlocks.map((block) => (
          <CategoryBlock
            key={block.type}
            {...block}
            onAdd={handleAddCategory}
            onRemove={handleRemoveCategory}
          />
        ))}
        <div className="flex justify-end pt-4">
          <Button
            type="button"
            className="bg-mobio-blue text-white hover:bg-mobio-blue/90"
            onClick={handleNextStep}
            disabled={isProcessing || !storyResponse?.data?.id || !tagGroups}
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                Next Step
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};