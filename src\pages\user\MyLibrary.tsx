import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { FileText, Video, Headphones, Image as ImageIcon, BookmarkX, Download, List, Grid } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

// Mock Data (same structure as other pages)
const savedStories = [
  {
    id: "1",
    title: "AI-Powered Inventory Management Transformation",
    summary: "Global retailer improved efficiency by 40% using our AI inventory solution",
    industry: "Retail",
    region: "APAC",
    technology: "AI",
    hasVideo: true,
    hasAudio: false,
    hasPdf: true,
    hasImage: true,
    dateSaved: "2023-05-12T10:30:00Z",
  },
  {
    id: "3",
    title: "Financial Services Fraud Detection Enhancement",
    summary: "Leading bank reduced fraud by 87% using our machine learning solution",
    industry: "FinTech",
    region: "Europe",
    technology: "Machine Learning",
    hasVideo: true,
    hasAudio: true,
    hasPdf: true,
    hasImage: false,
    dateSaved: "2023-05-15T14:45:00Z",
  },
];

const recentlyViewed = [
  {
    id: "2",
    title: "Healthcare Patient Experience Revolution",
    summary: "Major hospital chain reduced wait times by 60% with our patient management platform",
    industry: "Healthcare",
    region: "US",
    technology: "Data Analytics",
    hasVideo: false,
    hasAudio: true,
    hasPdf: true,
    hasImage: true,
    viewedOn: "2023-05-20T09:15:00Z",
    dateSaved: "2023-05-10T09:15:00Z", // Added missing dateSaved property
  },
  {
    id: "4",
    title: "Supply Chain Optimization Solution",
    summary: "E-commerce leader reduced shipping costs by 32% with our logistics platform",
    industry: "Logistics",
    region: "Global",
    technology: "IoT",
    hasVideo: false,
    hasAudio: false,
    hasPdf: true,
    hasImage: true,
    viewedOn: "2023-05-19T16:20:00Z",
    dateSaved: "2023-05-08T16:20:00Z", // Added missing dateSaved property
  },
  {
    id: "1",
    title: "AI-Powered Inventory Management Transformation",
    summary: "Global retailer improved efficiency by 40% using our AI inventory solution",
    industry: "Retail",
    region: "APAC",
    technology: "AI",
    hasVideo: true,
    hasAudio: false,
    hasPdf: true,
    hasImage: true,
    viewedOn: "2023-05-18T11:30:00Z",
    dateSaved: "2023-05-12T10:30:00Z", // Added missing dateSaved property
  },
];

const StoryCard = ({ story, onRemove }: { story: typeof savedStories[0], onRemove?: () => void }) => (
  <Card className="w-full hover:shadow-md transition-shadow">
    <CardContent className="p-4">
      <h3 className="font-medium text-base mb-1">
        <Link to={`/story/${story.id}`} className="hover:text-primary">
          {story.title}
        </Link>
      </h3>
      <p className="text-muted-foreground text-sm mb-3 line-clamp-2">{story.summary}</p>
      <div className="flex flex-wrap gap-2 mb-3">
        <Badge variant="outline" className="bg-mobio-lavender">{story.industry}</Badge>
        <Badge variant="outline" className="bg-mobio-lightblue">{story.region}</Badge>
        <Badge variant="outline" className="bg-mobio-gray">{story.technology}</Badge>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-muted-foreground">
          {story.hasPdf && <FileText className="h-4 w-4" />}
          {story.hasVideo && <Video className="h-4 w-4" />}
          {story.hasAudio && <Headphones className="h-4 w-4" />}
          {story.hasImage && <ImageIcon className="h-4 w-4" />}
        </div>
        <div className="flex items-center gap-2">
          {onRemove && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onRemove}
              className="h-8 w-8 p-0"
            >
              <BookmarkX className="h-4 w-4 text-muted-foreground hover:text-destructive" />
            </Button>
          )}
          <Button variant="outline" size="sm" asChild>
            <Link to={`/story/${story.id}`}>View</Link>
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
);

const MyLibrary = () => {
  const [viewType, setViewType] = useState<"grid" | "list">("grid");
  const [savedItems, setSavedItems] = useState(savedStories);

  const handleRemoveItem = (id: string) => {
    setSavedItems(savedItems.filter(item => item.id !== id));
    toast({
      title: "Removed from library",
      description: "The story has been removed from your saved items",
    });
  };

  const handleDownloadAll = () => {
    toast({
      title: "Downloading all stories",
      description: "Your stories are being prepared for download",
    });
  };

  return (
    <div className="container max-w-5xl mx-auto space-y-6 pb-16 md:pb-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-2xl font-bold">My Library</h1>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1"
            onClick={handleDownloadAll}
            disabled={savedItems.length === 0}
          >
            <Download className="h-4 w-4" /> Download All
          </Button>
          
          <div className="border rounded-md flex">
            <Button
              variant={viewType === "grid" ? "secondary" : "ghost"}
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setViewType("grid")}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewType === "list" ? "secondary" : "ghost"}
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setViewType("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      
      <Tabs defaultValue="saved">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="saved">Saved Stories</TabsTrigger>
          <TabsTrigger value="recent">Recently Viewed</TabsTrigger>
        </TabsList>
        
        <TabsContent value="saved" className="mt-4">
          {savedItems.length > 0 ? (
            <div className={`grid ${viewType === "grid" ? "grid-cols-1 md:grid-cols-2" : "grid-cols-1"} gap-4`}>
              {savedItems.map((story) => (
                <StoryCard 
                  key={story.id} 
                  story={story} 
                  onRemove={() => handleRemoveItem(story.id)} 
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 border rounded-md bg-muted/20">
              <h3 className="text-lg font-medium mb-2">No saved stories</h3>
              <p className="text-muted-foreground mb-4">
                You haven't saved any success stories to your library yet.
              </p>
              <Button asChild>
                <Link to="/browse">Browse Stories</Link>
              </Button>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="recent" className="mt-4">
          {recentlyViewed.length > 0 ? (
            <div className={`grid ${viewType === "grid" ? "grid-cols-1 md:grid-cols-2" : "grid-cols-1"} gap-4`}>
              {recentlyViewed.map((story) => (
                <StoryCard key={story.id} story={story} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 border rounded-md bg-muted/20">
              <h3 className="text-lg font-medium mb-2">No recently viewed stories</h3>
              <p className="text-muted-foreground mb-4">
                You haven't viewed any success stories recently.
              </p>
              <Button asChild>
                <Link to="/browse">Browse Stories</Link>
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MyLibrary;
