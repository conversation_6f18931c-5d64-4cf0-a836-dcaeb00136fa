
export interface Story {
  id: string;
  title: string;
  customer: string;  // Add this line
  clientSegment: string;
  oneLine: string;
  businessChallenges: string;
  summary: string;
  successStory: string;
  publishDate: Date;
  status: "draft" | "published" | "archived";
  confidential: boolean;
  categories: {
    industry: string[];
    technology: string[];
    businessProblem: string[];
    region: string[];
    outcome: string[];
    general_tag: string[]; // This matches your SQL schema
  };
  context: {
    background: string;
    challenge: string;
    solution: string;
    benefits: string;
    results: string;
    quote: string;
    quoteAuthor: string;
    quotePosition: string;
  };
  attachments: Attachment[];
  credentials: Credential[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  demoLink: string;
  demoAccessDetails: string;
  showDemoToEndUsers: boolean;
  roleVisibility: {
    cxo: boolean;
    sales: boolean;
    presales: boolean;
    marketing: boolean;
    delivery: boolean;
  };
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  url: string;
  previewUrl: string;
}

export interface Credential {
  id: string;
  role: string;
  email: string;
  password: string;
}

export interface CategoryOption {
  id: string;
  name: string;
  category?: string;
}

export interface AiState {
  source: 'pdf' | 'url' | 'text';
  sourceUrl: string;
  isProcessing: boolean;
  processed: boolean;
}

export interface SuggestedTag {
  id: string;
  name: string;
  category: string;
}

export interface FileState {
  pdf: File | null;
  image: File | null;
  document: File | null;
}
