import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ExternalLink } from "lucide-react";

interface Credential {
  role: string;
  email: string;
  password: string;
}

interface DemoMediaItem {
  name?: string;
  url: string; // Assuming URL is always present for a demo item
  credentials?: Credential[];
}

interface DemoMediaDisplayProps {
  demoMediaItems: DemoMediaItem[];
}

const DemoMediaDisplay = ({ demoMediaItems }: DemoMediaDisplayProps) => {
  if (!demoMediaItems || demoMediaItems.length === 0) {
    return (
      <div className="flex h-32 items-center justify-center border border-dashed rounded-lg bg-slate-50 mx-auto space-y-6 p-4 sm:p-6 w-full max-w-[73rem]">
        <p className="text-muted-foreground text-center px-4">
          No demo access available for this success story
        </p>
      </div>
    );
  }

  return (
    <div className="mx-auto space-y-6 p-4 sm:p-6 w-full max-w-[73rem]">
      {demoMediaItems.map((item) => (
        <div
          key={item.url}
          className="bg-white shadow-sm border rounded-lg p-4 sm:p-6 space-y-4"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0">
            <div className="flex items-center space-x-3">
              <ExternalLink className="h-6 w-6 text-purple-500 flex-shrink-0" />
              <div className="min-w-0">
                <h3 className="text-lg font-medium truncate">
                  {item.name ?? "Demo Access"}
                </h3>
                <p className="text-sm text-muted-foreground truncate">
                  {item.url}
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(item.url, "_blank")}
              className="hover:bg-slate-50 w-full sm:w-auto"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Open Demo
            </Button>
          </div>

          {item.credentials && item.credentials.length > 0 && (
            <div className="mt-6 overflow-x-auto">
              <h4 className="text-sm font-medium mb-3">
                Access Credentials
              </h4>
              <div className="bg-slate-50 rounded-lg overflow-hidden border">
                <Table>
                  <TableHeader>
                    <TableRow className="hover:bg-transparent">
                      <TableHead className="w-1/3">Role</TableHead>
                      <TableHead className="w-1/3">
                        Email/Username
                      </TableHead>
                      <TableHead className="w-1/3">Password</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {item.credentials.map((cred) => (
                      <TableRow
                        key={cred.email} // Assuming email is unique within a demo item's credentials
                        className="hover:bg-white/50"
                      >
                        <TableCell className="font-medium">
                          {cred.role}
                        </TableCell>
                        <TableCell className="break-all">
                          {cred.email}
                        </TableCell>
                        <TableCell className="break-all">
                          {cred.password}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default DemoMediaDisplay;