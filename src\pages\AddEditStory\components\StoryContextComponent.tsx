
import React from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Sparkles } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useStory } from "../StoryContext";

const StoryContextComponent: React.FC = () => {
  const { 
    storyData, 
    handleContextChange, 
    aiPrompt, 
    setAiPrompt, 
    setIsAiPanelOpen, 
    isAiPanelOpen,
    handleAiContentGenerate,
    isProcessingAi 
  } = useStory();

  const context = storyData.context;

  const handleOpenAiPanel = () => {
    if (!aiPrompt) {
      // Pre-fill the AI prompt with relevant context
      setAiPrompt(
        `Generate a comprehensive success story about ${
          context.background ? context.background : "the client"
        } focusing on their business challenge and our solution.`
      );
    }
    setIsAiPanelOpen(true);
  };

  return (
    <Card className="border-mobio-lavender/40">
      <CardHeader className="bg-mobio-lavender/10">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-mobio-blue">Story Context</CardTitle>
            <CardDescription>
              Provide the details about the customer success story
            </CardDescription>
          </div>
          <Button
            onClick={handleOpenAiPanel}
            size="sm"
            className="bg-mobio-blue hover:bg-mobio-blue/90"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            AI Generate
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 pt-5">
        <div>
          <Label htmlFor="background" className="text-base font-medium">
            Background <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="background"
            placeholder="Describe the client's company, industry, size, and relevant context"
            value={context.background}
            onChange={(e) => handleContextChange("background", e.target.value)}
            className="mt-1.5 min-h-[100px]"
          />
        </div>

        <div>
          <Label htmlFor="challenge" className="text-base font-medium">
            Challenge <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="challenge"
            placeholder="What specific problem or challenge did the client face?"
            value={context.challenge}
            onChange={(e) => handleContextChange("challenge", e.target.value)}
            className="mt-1.5 min-h-[100px]"
          />
        </div>

        <div>
          <Label htmlFor="solution" className="text-base font-medium">
            Solution <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="solution"
            placeholder="How did our product or service solve the client's problem?"
            value={context.solution}
            onChange={(e) => handleContextChange("solution", e.target.value)}
            className="mt-1.5 min-h-[100px]"
          />
        </div>

        <div>
          <Label htmlFor="benefits" className="text-base font-medium">
            Benefits <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="benefits"
            placeholder="What specific features or aspects of the solution provided value?"
            value={context.benefits}
            onChange={(e) => handleContextChange("benefits", e.target.value)}
            className="mt-1.5 min-h-[100px]"
          />
        </div>

        <div>
          <Label htmlFor="results" className="text-base font-medium">
            Results <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="results"
            placeholder="What measurable outcomes or improvements did the client achieve?"
            value={context.results}
            onChange={(e) => handleContextChange("results", e.target.value)}
            className="mt-1.5 min-h-[100px]"
          />
        </div>

        <div>
          <Label htmlFor="quote" className="text-base font-medium">
            Customer Quote
          </Label>
          <Textarea
            id="quote"
            placeholder="Add a testimonial or quote from the client (optional)"
            value={context.quote}
            onChange={(e) => handleContextChange("quote", e.target.value)}
            className="mt-1.5"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="quoteAuthor" className="text-base font-medium">
              Quote Author
            </Label>
            <Textarea
              id="quoteAuthor"
              placeholder="Who provided the testimonial?"
              value={context.quoteAuthor}
              onChange={(e) => handleContextChange("quoteAuthor", e.target.value)}
              className="mt-1.5"
            />
          </div>

          <div>
            <Label htmlFor="quotePosition" className="text-base font-medium">
              Author's Position
            </Label>
            <Textarea
              id="quotePosition"
              placeholder="What is their role or title?"
              value={context.quotePosition}
              onChange={(e) =>
                handleContextChange("quotePosition", e.target.value)
              }
              className="mt-1.5"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StoryContextComponent;
