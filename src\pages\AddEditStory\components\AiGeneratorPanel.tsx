
import { Wand2, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface AiGeneratorPanelProps {
  isOpen: boolean;
  onClose: () => void;
  aiPrompt: string;
  setAiPrompt: React.Dispatch<React.SetStateAction<string>>;
  isProcessingAi: boolean;
  onGenerateContent: () => void;
}

export const AiGeneratorPanel = ({
  isOpen,
  onClose,
  aiPrompt,
  setAiPrompt,
  isProcessingAi,
  onGenerateContent
}: AiGeneratorPanelProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="w-full max-w-2xl p-6 bg-card rounded-lg shadow-lg border">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold flex items-center">
            <Wand2 className="h-5 w-5 mr-2 text-mobio-blue" /> 
            Generate Story Content with AI
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>×</Button>
        </div>
        
        <div className="mb-6">
          <Label htmlFor="ai-prompt" className="text-base font-medium">Prompt for AI</Label>
          <Textarea
            id="ai-prompt"
            value={aiPrompt}
            onChange={(e) => setAiPrompt(e.target.value)}
            placeholder="Describe the success story context, e.g., 'Generate a success story about an AI-driven inventory management solution for a Fortune 500 retailer that reduced costs by 25%.'"
            className="min-h-[150px] mt-2"
          />
        </div>
        
        <div className="flex justify-end space-x-3">
          <Button 
            variant="outline" 
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button 
            onClick={onGenerateContent}
            disabled={isProcessingAi || !aiPrompt.trim()}
            className="bg-mobio-blue hover:bg-mobio-blue/90"
          >
            {isProcessingAi ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4 mr-2" />
                Generate Content
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};
