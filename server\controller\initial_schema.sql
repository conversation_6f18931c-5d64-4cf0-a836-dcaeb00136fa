import authService from "../services/auth.service.js";

export class AuthController {
  async register(req, res) {
    try {
      // Log the incoming request body for debugging
      console.log('Registration request body:', JSON.stringify(req.body, null, 2));
      
      const result = await authService.register(req.body);
      
      res.status(201).json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('Registration controller error:', error);
      
      // Provide more specific error messages based on error type
      if (error.message.includes('already exists')) {
        return res.status(409).json({
          success: false,
          message: error.message,
        });
      }
      
      // Handle validation errors
      if (error.name === 'ZodError') {
        return res.status(400).json({
          success: false,
          message: 'Validation error',
          details: error.errors,
        });
      }
      
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : "Registration failed",
      });
    }
  }

  async login(req, res) {
    try {
      const { email, password } = req.body;
      const result = await authService.login(email, password);
      res.status(200).json({
        success: true,
        data: result,
      });
    } catch (error) {
      res.status(401).json({
        success: false,
        message: error instanceof Error ? error.message : "Authentication failed",
      });
    }
  }

  async getProfile(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: "Not authorized",
        });
        return;
      }
      const profile = await authService.getProfile(userId);
      res.status(200).json({
        success: true,
        data: profile,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : "Failed to fetch profile",
      });
    }
  }

  async getAllUsers(req, res) {
    try {
      const users = await authService.getAllUsers();
      res.status(200).json({
        success: true,
        data: users
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : "Failed to fetch users"
      });
    }
  }

  async getUserById(req, res) {
    try {
      const userId = req.params.id;
      const user = await authService.getUserById(userId);
      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : "Failed to fetch user"
      });
    }
  }

  async updateUser(req, res) {
    try {
      const userId = req.params.id;
      const updatedUser = await authService.updateUser(userId, req.body);
      res.status(200).json({
        success: true,
        data: updatedUser
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : "Failed to update user"
      });
    }
  }

  async deleteUser(req, res) {
    try {
      const userId = req.params.id;
      const currentUserId = req.user.id;
      const deletedUser = await authService.deleteUser(userId, currentUserId);
      res.status(200).json({
        success: true,
        data: deletedUser
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : "Failed to delete user"
      });
    }
  }

  async logout(req, res) {
    try {
      const userId = req.user?.id; // Extracted by authenticateToken middleware
      if (!userId) {
        // This case should ideally not be reached if authenticateToken is working correctly
        return res.status(401).json({
          success: false,
          message: "User not authenticated.",
        });
      }

      await authService.logout(userId);
      
      res.status(200).json({
        success: true,
        message: "Logged out successfully.",
      });
    } catch (error) {
      // Log the error on the server for debugging
      console.error("Logout controller error:", error.message);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : "Logout failed due to a server error.",
      });
    }
  }

  async refreshToken(req, res) {
    try {
      const { refreshToken: clientRefreshToken } = req.body;
      if (!clientRefreshToken) {
        return res.status(400).json({
          success: false,
          message: "Refresh token is required in the request body.",
        });
      }

      const result = await authService.refreshToken(clientRefreshToken);
      
      res.status(200).json({
        success: true,
        data: result, // Contains new user and session (new access_token, new refresh_token)
      });
    } catch (error) {
      console.error("Refresh token controller error:", error.message);
      // Supabase refresh errors might be specific, e.g., invalid grant
      // We can return a 401 or 403 for invalid/expired refresh tokens
      if (error.message.includes("Invalid refresh token") || error.message.includes("Failed to refresh session")) {
         return res.status(401).json({ // 401 Unauthorized or 403 Forbidden
          success: false,
          message: "Invalid or expired refresh token. Please log in again.",
        });
      }
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : "Token refresh failed due to a server error.",
      });
    }
  }

  async updateProfile(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Not authorized",
        });
      }
      
      const updatedProfile = await authService.updateProfile(userId, req.body);
      res.status(200).json({
        success: true,
        data: updatedProfile,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : "Failed to update profile",
      });
    }
  }
}

export default new AuthController();




