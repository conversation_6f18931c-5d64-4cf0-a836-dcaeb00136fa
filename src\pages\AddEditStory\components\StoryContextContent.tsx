
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Wand2 } from "lucide-react";

interface StoryContextProps {
  context: {
    background: string;
    challenge: string;
    solution: string;
    benefits: string;
    results: string;
    quote: string;
    quoteAuthor: string;
    quotePosition: string;
  };
  onContextChange: (field: string, value: string) => void;
  onOpenAiPanel: () => void;
}

export const StoryContextContent = ({ context, onContextChange, onOpenAiPanel }: StoryContextProps) => {
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Story Context</CardTitle>
            <CardDescription>
              Define the narrative of your success story
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            onClick={onOpenAiPanel}
            className="flex items-center"
          >
            <Wand2 className="mr-2 h-4 w-4" />
            Generate with AI
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="background" className="text-base font-medium">Background</Label>
          <Textarea
            id="background"
            value={context.background}
            onChange={(e) => onContextChange('background', e.target.value)}
            placeholder="Describe the client's industry, size, and market position"
            className="min-h-[100px] mt-1.5"
          />
        </div>
        
        <div>
          <Label htmlFor="challenge" className="text-base font-medium">Challenge</Label>
          <Textarea
            id="challenge"
            value={context.challenge}
            onChange={(e) => onContextChange('challenge', e.target.value)}
            placeholder="Explain the specific problem the client was facing"
            className="min-h-[100px] mt-1.5"
          />
        </div>
        
        <div>
          <Label htmlFor="solution" className="text-base font-medium">Solution</Label>
          <Textarea
            id="solution"
            value={context.solution}
            onChange={(e) => onContextChange('solution', e.target.value)}
            placeholder="Detail how our approach solved their problem"
            className="min-h-[100px] mt-1.5"
          />
        </div>
        
        <div>
          <Label htmlFor="benefits" className="text-base font-medium">Benefits</Label>
          <Textarea
            id="benefits"
            value={context.benefits}
            onChange={(e) => onContextChange('benefits', e.target.value)}
            placeholder="Highlight key features and advantages of the solution"
            className="min-h-[100px] mt-1.5"
          />
        </div>
        
        <div>
          <Label htmlFor="results" className="text-base font-medium">Results</Label>
          <Textarea
            id="results"
            value={context.results}
            onChange={(e) => onContextChange('results', e.target.value)}
            placeholder="Quantify the impact and outcomes achieved"
            className="min-h-[100px] mt-1.5"
          />
        </div>
        
        <div className="bg-muted/30 p-4 rounded-md">
          <Label htmlFor="quote" className="text-base font-medium">Client Testimonial</Label>
          <Textarea
            id="quote"
            value={context.quote}
            onChange={(e) => onContextChange('quote', e.target.value)}
            placeholder="Add a notable quote from the client"
            className="min-h-[100px] mt-1.5"
          />
          
          <div className="grid grid-cols-2 gap-4 mt-4">
            <div>
              <Label htmlFor="quoteAuthor" className="text-sm">Quote Author</Label>
              <Textarea
                id="quoteAuthor"
                value={context.quoteAuthor}
                onChange={(e) => onContextChange('quoteAuthor', e.target.value)}
                placeholder="Name of the person quoted"
                className="min-h-[60px] mt-1"
              />
            </div>
            <div>
              <Label htmlFor="quotePosition" className="text-sm">Position/Title</Label>
              <Textarea
                id="quotePosition"
                value={context.quotePosition}
                onChange={(e) => onContextChange('quotePosition', e.target.value)}
                placeholder="Role of the person quoted"
                className="min-h-[60px] mt-1"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
