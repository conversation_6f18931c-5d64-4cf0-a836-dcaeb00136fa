-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  role TEXT DEFAULT 'user',
  created_at TIMESTAMPTZ DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMPTZ DEFAULT TIMEZONE('utc', NOW()),
  created_by UUID,
  updated_by UUID,
  flag_active BOOLEAN DEFAULT true,
  flag_deleted BOOLEAN DEFAULT false,
  
  -- Profile fields
  first_name TEXT,
  last_name TEXT,
  phone TEXT,
  profile_picture_url TEXT,
  designation TEXT,
  department TEXT,
  location TEXT,
  bio TEXT
);

-- Tags table (industry, region, technology, etc.)
CREATE TABLE IF NOT EXISTS tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('industry', 'region', 'technology', 'outcome', 'business_problem', 'general_tag')),
  status BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'utc'),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'utc'),
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
  flag_deleted BOOLEAN DEFAULT false,
  UNIQUE(name, type)
);

CREATE TABLE IF NOT EXISTS success_stories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

  title TEXT NOT NULL,
  executive_summary TEXT,
  client_segment TEXT,
  oneline_summary TEXT,
  business_challenges TEXT,
  success_story_detail TEXT,

  industries UUID[],
  regions UUID[],
  technologies UUID[],
  outcomes UUID[],
  general_tags UUID[],

  publicAppearance BOOLEAN DEFAULT false,

  image_assets JSONB DEFAULT '[]',
  audio_assets JSONB DEFAULT '[]',
  video_assets JSONB DEFAULT '[]',
  document_assets JSONB DEFAULT '[]',
  demo_assets JSONB DEFAULT '[]',

  owner_id UUID REFERENCES users(id),

  status TEXT NOT NULL CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft',

  created_at TIMESTAMPTZ DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMPTZ DEFAULT TIMEZONE('utc', NOW()),

  created_by UUID REFERENCES users(id),
  updated_by UUID REFERENCES users(id),

  flag_deleted BOOLEAN DEFAULT false,

  CONSTRAINT chk_technologies CHECK (technologies IS NULL OR array_length(technologies, 1) > 0),
  CONSTRAINT chk_outcomes CHECK (outcomes IS NULL OR array_length(outcomes, 1) > 0)
);

ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE tags DISABLE ROW LEVEL SECURITY;
ALTER TABLE success_stories DISABLE ROW LEVEL SECURITY;

-- ... existing code ...



