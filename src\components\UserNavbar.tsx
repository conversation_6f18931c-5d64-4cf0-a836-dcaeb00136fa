import { useState } from "react";
import { Link, NavLink, useLocation, useNavigate } from "react-router-dom";
import {
  Mic,
  Search,
  Bell,
  BookMarked,
  Settings,
  Menu,
  LogOut,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";

// Separate components for better organization
const Logo = () => (
  <Link
    to="/"
    className="flex items-center gap-2 hover-transition hover:scale-105 mr-4"
  >
    <img
      src="/lovable-uploads/apple-touch-icon.png"
      alt="Mobio Logo"
      className="h-8"
    />
    <span className="font-poppins font-semibold text-mobio-blue text-xl hidden md:block">
      WinWise
    </span>
  </Link>
);

const SearchBar = ({
  searchQuery,
  handleSearchChange,
  handleFormSubmit,
  handleVoiceSearch,
  isListening,
  className = "",
}) => (
  <form onSubmit={handleFormSubmit} className={`relative ${className}`}>
    <Input
      type="text"
      placeholder="Enter a success story title to search..."
      className="pl-10 pr-10 h-10 rounded-full bg-background transition-shadow focus-within:shadow-md w-full"
      value={searchQuery}
      onChange={handleSearchChange}
    />
    <Search className="absolute left-3 top-[10px] h-5 w-5 text-muted-foreground pointer-events-none" />
    <Button
      type="button"
      variant="ghost"
      size="icon"
      className={`absolute right-1 top-[2px] h-8 w-8 rounded-full transition-all duration-300 ${
        isListening
          ? "text-blue-500 before:absolute before:inset-0 before:rounded-full before:animate-ping before:bg-blue-500/50 after:absolute after:inset-0 after:rounded-full after:animate-pulse after:bg-blue-500/30"
          : "hover:bg-primary/10"
      }`}
      onClick={handleVoiceSearch}
      title="Tap to speak your search – try 'AI in Healthcare in Europe'"
    >
      <Mic className={`h-4 w-4 ${isListening ? "animate-bounce" : ""}`} />
    </Button>
  </form>
);

const DesktopNav = ({ location }) => (
  <NavigationMenu className="hidden md:flex">
    <NavigationMenuList>
      <NavigationMenuItem>
        <Link
          to="/"
          className={`${navigationMenuTriggerStyle()} ${
            location.pathname === "/" ? "text-primary" : ""
          }`}
        >
          Home
        </Link>
      </NavigationMenuItem>
    </NavigationMenuList>
  </NavigationMenu>
);

const UserActions = ({ handleLogout }) => (
  <div className="hidden md:flex items-center gap-1 sm:gap-2">
    <Button
      variant="ghost"
      size="icon"
      asChild
      className="hover-transition btn-hover"
    >
      <Link to="/library" title="My Library">
        <BookMarked className="h-5 w-5" />
      </Link>
    </Button>
    <Button
      variant="ghost"
      size="icon"
      asChild
      className="hover-transition btn-hover"
    >
      <Link to="/settings" title="Settings">
        <Settings className="h-5 w-5" />
      </Link>
    </Button>
    <Button
      variant="ghost"
      size="icon"
      onClick={handleLogout}
      className="hover-transition btn-hover"
      title="Logout"
    >
      <LogOut className="h-5 w-5" />
    </Button>
  </div>
);

const UserDropdown = ({ handleLogout }) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button
        variant="ghost"
        className="relative h-8 w-8 rounded-full hover:bg-secondary/80 hover-transition hover:scale-105"
      >
        <Avatar className="h-8 w-8 hover-glow">
          <AvatarImage src="https://github.com/shadcn.png" alt="User" />
          <AvatarFallback>MS</AvatarFallback>
        </Avatar>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent
      className="w-56 animate-scale-in"
      align="end"
      forceMount
    >
      <DropdownMenuLabel className="font-normal">
        <div className="flex flex-col space-y-1">
          <p className="text-sm font-medium leading-none">Sarah Johnson</p>
          <p className="text-xs leading-none text-muted-foreground">
            <EMAIL>
          </p>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuItem className="hover-transition cursor-pointer" asChild>
        <Link to="/library">My Library</Link>
      </DropdownMenuItem>
      <DropdownMenuItem className="hover-transition cursor-pointer" asChild>
        <Link to="/settings">Settings</Link>
      </DropdownMenuItem>
      <DropdownMenuSeparator />
      <DropdownMenuItem className="hover-transition cursor-pointer" asChild>
        <Link to="/admin">Admin Dashboard</Link>
      </DropdownMenuItem>
      <DropdownMenuSeparator />
      <DropdownMenuItem
        className="hover-transition cursor-pointer text-red-500"
        onClick={handleLogout}
      >
        <LogOut className="h-4 w-4 mr-2" />
        Logout
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
);

const MobileMenu = ({
  searchQuery,
  setSearchQuery,
  handleFormSubmit,
  handleVoiceSearch,
  isListening,
  handleLogout,
}) => (
  <Sheet>
    <SheetTrigger asChild>
      <Button
        variant="ghost"
        size="icon"
        className="md:hidden hover-transition btn-hover"
      >
        <Menu className="h-5 w-5" />
      </Button>
    </SheetTrigger>
    <SheetContent side="right" className="w-[80vw] sm:w-[350px]">
      <div className="flex flex-col space-y-4 py-4">
        <form onSubmit={handleFormSubmit} className="relative">
          <Input
            type="text"
            placeholder="Search success stories..."
            className="pl-10 pr-10 h-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Search className="absolute left-3 top-[10px] h-5 w-5 text-muted-foreground pointer-events-none" />
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className={`absolute right-1 top-[2px] h-8 w-8 rounded-full ${
              isListening ? "text-red-500 animate-pulse" : "hover:bg-primary/10"
            }`}
            onClick={handleVoiceSearch}
          >
            <Mic className="h-4 w-4" />
          </Button>
        </form>

        <NavLink
          to="/"
          className={({ isActive }) =>
            `p-2 rounded-md transition-all duration-200 hover:translate-x-2 ${
              isActive ? "bg-muted text-primary" : "hover:bg-secondary/70"
            }`
          }
        >
          Home
        </NavLink>
        <NavLink
          to="/browse"
          className={({ isActive }) =>
            `p-2 rounded-md transition-all duration-200 hover:translate-x-2 ${
              isActive ? "bg-muted text-primary" : "hover:bg-secondary/70"
            }`
          }
        >
          Browse Stories
        </NavLink>
        <NavLink
          to="/library"
          className={({ isActive }) =>
            `p-2 rounded-md transition-all duration-200 hover:translate-x-2 ${
              isActive ? "bg-muted text-primary" : "hover:bg-secondary/70"
            }`
          }
        >
          My Library
        </NavLink>
        <NavLink
          to="/settings"
          className={({ isActive }) =>
            `p-2 rounded-md transition-all duration-200 hover:translate-x-2 ${
              isActive ? "bg-muted text-primary" : "hover:bg-secondary/70"
            }`
          }
        >
          Settings
        </NavLink>
        <button
          onClick={handleLogout}
          className="p-2 rounded-md transition-all duration-200 hover:translate-x-2 hover:bg-secondary/70 text-left flex items-center text-red-500"
        >
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </button>
      </div>
    </SheetContent>
  </Sheet>
);
const UserNavbar = () => {
  const [isListening, setIsListening] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();

  const handleVoiceSearch = () => {
    if (!("webkitSpeechRecognition" in window)) {
      toast({
        title: "Voice Search Unavailable",
        description: "Your browser doesn't support voice recognition.",
        variant: "destructive",
      });
      return;
    }

    const recognition = new (window as any).webkitSpeechRecognition();
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = "en-US";

    recognition.onstart = () => {
      setIsListening(true);
    };

    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript;
      setSearchQuery(transcript);

      // Simply dispatch the updateSearch event without URL changes
      window.dispatchEvent(
        new CustomEvent("updateSearch", {
          detail: { query: transcript },
        })
      );
    };

    recognition.onerror = (event: any) => {
      setIsListening(false);
    };

    recognition.onend = () => {
      setIsListening(false);
    };

    recognition.start();
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);

    // Simply dispatch the updateSearch event without URL changes
    window.dispatchEvent(
      new CustomEvent("updateSearch", {
        detail: { query: value },
      })
    );
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery) {
      // Simply dispatch the updateSearch event without URL changes
      window.dispatchEvent(
        new CustomEvent("updateSearch", {
          detail: { query: searchQuery },
        })
      );
    }
  };

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  return (
    <>
      <header className="sticky top-0 z-40 w-full border-b bg-white">
        <div className="flex h-16 items-center justify-between w-full px-2 sm:px-4">
          <div className="flex items-center">
            <Logo />
            <DesktopNav location={location} />
          </div>

          <div className="flex items-center gap-2 sm:gap-4">
            <UserActions handleLogout={handleLogout} />
            <UserDropdown handleLogout={handleLogout} />
            <MobileMenu
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              handleFormSubmit={handleFormSubmit}
              handleVoiceSearch={handleVoiceSearch}
              isListening={isListening}
              handleLogout={handleLogout}
            />
          </div>
        </div>
      </header>

      {/* Search Bar Section - Below Navbar */}
      <div className="w-full bg-white border-b px-2 sm:px-4 py-3">
        <div className="container mx-auto max-w-5xl">
          <SearchBar
            searchQuery={searchQuery}
            handleSearchChange={handleSearchChange}
            handleFormSubmit={handleFormSubmit}
            handleVoiceSearch={handleVoiceSearch}
            isListening={isListening}
          />
        </div>
      </div>
    </>
  );
};

export default UserNavbar;
