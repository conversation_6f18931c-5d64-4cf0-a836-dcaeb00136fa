import { PDFExtract } from "pdf.js-extract";
import textract from "textract";
import axios from "axios";
import { supabase } from "../config/db.js";

class DocumentService {
  async extractContent(file) {
    try {
      let content = "";

      if (file.mimetype === "application/pdf") {
        const pdfExtract = new PDFExtract();
        const data = await pdfExtract.extractBuffer(file.buffer);
        content = data.pages
          .map((page) => {
            return page.content
              .map((item) => {
                return typeof item === "object" ? item.str || "" : String(item);
              })
              .join(" ");
          })
          .join("\n");
      } else if (
        file.mimetype === "application/msword" ||
        file.mimetype ===
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      ) {
        content = await new Promise((resolve, reject) => {
          textract.fromBufferWithMime(
            file.mimetype,
            file.buffer,
            (error, text) => {
              if (error) reject(error);
              resolve(text);
            }
          );
        });
      }

      await this.sendToN8n(content);
      return content;
    } catch (error) {
      console.error("Content extraction error:", error);
      throw new Error(`Failed to extract content: ${error.message}`);
    }
  }

  async getTagsByType(type) {
    try {
      const { data, error } = await supabase
        .from("tags")
        .select("name")
        .eq("type", type)
        .eq("flag_deleted", false)
        .order("name");

      if (error) throw error;
      return data.map((tag) => tag.name);
    } catch (error) {
      console.error(`Error fetching ${type} tags:`, error);
      return [];
    }
  }

  async sendToN8n(message, url) {
    try {
      const messageContent =
        typeof message === "object" ? JSON.stringify(message) : String(message);
      const sanitizedUrl = Array.isArray(url) ? url[0] : url;
      const cleanUrl = sanitizedUrl
        ? sanitizedUrl.replace(/`/g, "").trim()
        : "";

      // Fetch tags by type from the database
      const industries = await this.getTagsByType("industry");
      const technologies = await this.getTagsByType("technology");
      const businessProblems = await this.getTagsByType("business_problem");
      const outcomes = await this.getTagsByType("outcome");

      const response = await axios.post(
        process.env.N8N_WEBHOOK_URL,
        {
          message: cleanUrl ? "" : messageContent,
          url: cleanUrl || "",
          industry: industries,
          technology: technologies,
          businessProblem: businessProblems,
          outcome: outcomes,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error("N8n webhook error:", error);
      throw new Error(`Failed to send to n8n: ${error.message}`);
    }
  }

  async storeSuccessStory(n8nResponse, userId) {
    try {
      const content = n8nResponse.message.content;

      // Extract basic information
      const storyData = {
        title: content.BasicInformation.Title,
        executive_summary: content.BasicInformation.ExecutiveSummary,
        client_segment: content.BasicInformation.ClientSegment,
        oneline_summary: content.BasicInformation.OneLine_Summary,
        business_challenges: content.BasicInformation.BusinessChallenges,
        success_story_detail: content.SuccessStoryDetail,
        technologies: content.Categorization.Technologies,
        outcomes: content.Categorization.Outcomes,
        tags: content.Tags,
        created_by: userId,
        updated_by: userId,
        industries: [], // Initialize with empty array to prevent null
        regions: [], // Initialize with empty array to prevent null
      };

      // Fetch industry tag if Industry is provided
      if (content.Categorization.Industry) {
        const { data: industryTag, error: industryError } = await supabase
          .from("tags")
          .select("id")
          .eq("name", content.Categorization.Industry)
          .eq("type", "industry")
          .single();

        if (industryError) {
          console.error(`Industry tag not found: ${industryError.message}`);
          // Don't throw error, continue with empty industries array
        } else {
          storyData.industries = [industryTag.id];
        }
      }

      // Fetch region tags if Region is provided
      if (
        content.Categorization.Region &&
        content.Categorization.Region.length > 0
      ) {
        const { data: regionTags, error: regionError } = await supabase
          .from("tags")
          .select("id")
          .in("name", content.Categorization.Region)
          .eq("type", "region");

        if (regionError) {
          console.error(`Region tags not found: ${regionError.message}`);
          // Don't throw error, continue with empty regions array
        } else {
          storyData.regions = regionTags.map((tag) => tag.id);
        }
      }

      const { data: story, error } = await supabase
        .from("success_stories")
        .insert([storyData])
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to store success story: ${error.message}`);
      }

      return story;
    } catch (error) {
      console.error("Error storing success story:", error);
      throw error;
    }
  }
}

export default new DocumentService();
