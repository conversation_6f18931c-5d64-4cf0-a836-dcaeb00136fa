
import { useState } from "react";
import { Search, Mic } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";

interface SearchBarProps {
  searchTerm: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const SearchBar = ({ searchTerm, onSearchChange }: SearchBarProps) => {
  const [isListening, setIsListening] = useState(false);

  const handleVoiceSearch = () => {
    if (!("webkitSpeechRecognition" in window)) {
      toast({
        title: "Voice Search Unavailable",
        description: "Your browser doesn't support voice recognition.",
        variant: "destructive",
      });
      return;
    }

    const recognition = new (window as any).webkitSpeechRecognition();
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = "en-US";

    recognition.onstart = () => {
      setIsListening(true);
    };

    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript;
      // Create a synthetic event to pass to onSearchChange
      const syntheticEvent = {
        target: { value: transcript },
      } as React.ChangeEvent<HTMLInputElement>;
      onSearchChange(syntheticEvent);
    };

    recognition.onerror = () => {
      setIsListening(false);
    };

    recognition.onend = () => {
      setIsListening(false);
    };

    recognition.start();
  };

  return (
    <div className="relative flex-1 overflow-hidden z-10">
      <div className="relative flex w-full items-center">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none z-20" />
        <Input
          placeholder="Search for success stories..."
          className="pl-9 pr-10 border-2 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
          value={searchTerm}
          onChange={onSearchChange}
        />
        <Button
          variant="ghost"
          size="icon"
          className={`absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 rounded-full transition-all duration-300 z-20 ${
            isListening
              ? "text-blue-500 before:absolute before:inset-0 before:rounded-full before:animate-ping before:bg-blue-500/50 after:absolute after:inset-0 after:rounded-full after:animate-pulse after:bg-blue-500/30"
              : "hover:bg-primary/10"
          }`}
          title="Tap to speak your search"
          onClick={handleVoiceSearch}
        >
          <Mic className={`h-4 w-4 ${isListening ? "animate-bounce" : ""}`} />
        </Button>
      </div>
    </div>
  );
};

export default SearchBar;
