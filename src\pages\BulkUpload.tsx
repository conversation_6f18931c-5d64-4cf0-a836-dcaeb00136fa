
import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  UploadCloud,
  FileSpreadsheet,
  Check,
  X,
  AlertCircle,
  Sparkles,
  RefreshCw,
  ChevronRight,
  Link as LinkIcon,
  ExternalLink,
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Progress } from "@/components/ui/progress";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

// Mock data for the steps
const stepsData = [
  { id: 1, name: "Upload Files" },
  { id: 2, name: "Map Fields" },
  { id: 3, name: "Review & Validate" },
  { id: 4, name: "Upload Results" },
];

// Mock data for story mappings
const storiesMockData = [
  {
    id: "1",
    title: "AI Optimization for Major E-commerce Platform",
    status: "success",
    industry: "Retail",
    region: "North America",
    technology: "AI/ML",
  },
  {
    id: "2",
    title: "Healthcare Patient Management System",
    status: "warning",
    industry: "Healthcare",
    region: "Europe",
    technology: "Cloud Solutions",
  },
  {
    id: "3",
    title: "Banking Fraud Detection Enhancement",
    status: "success",
    industry: "Financial Services",
    region: "Global",
    technology: "Machine Learning",
  },
  {
    id: "4",
    title: "Supply Chain Optimization for Manufacturing",
    status: "success",
    industry: "Manufacturing",
    region: "Asia",
    technology: "IoT",
  },
  {
    id: "5",
    title: "Mobile App Development for Retail Chain",
    status: "error",
    industry: "Retail",
    region: "Australia",
    technology: "Mobile",
  },
  {
    id: "6",
    title: "Data Integration Platform for Insurance",
    status: "success",
    industry: "Insurance",
    region: "Europe",
    technology: "Data Engineering",
  },
  {
    id: "7",
    title: "Customer Analytics Dashboard",
    status: "warning",
    industry: "Multiple",
    region: "North America",
    technology: "Analytics",
  },
];

// Mock data for field mappings
const fieldMappingsMockData = [
  { excelField: "Story Title", systemField: "title", required: true, mapped: true },
  { excelField: "Client Industry", systemField: "industry", required: true, mapped: true },
  { excelField: "Geography", systemField: "region", required: true, mapped: true },
  { excelField: "Tech Stack", systemField: "technology", required: true, mapped: true },
  { excelField: "Case Summary", systemField: "summary", required: true, mapped: true },
  { excelField: "Business Challenge", systemField: "businessProblem", required: true, mapped: true },
  { excelField: "Main Outcome", systemField: "outcomes", required: true, mapped: true },
  { excelField: "Case Study Date", systemField: "date", required: false, mapped: true },
  { excelField: "Client Size", systemField: "clientSize", required: false, mapped: false },
  { excelField: "Sales Lead", systemField: "salesLead", required: false, mapped: false },
];

// Component for the upload results
const UploadResultsView = ({ results }: { results: any[] }) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Upload Results</h3>
          <p className="text-sm text-muted-foreground">
            {results.filter(r => r.status === "success").length} of {results.length} stories uploaded successfully
          </p>
        </div>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          <RefreshCw className="h-4 w-4" /> Retry Failed
        </Button>
      </div>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Status</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Industry</TableHead>
              <TableHead>Region</TableHead>
              <TableHead>Technology</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {results.map((story) => (
              <TableRow key={story.id}>
                <TableCell>
                  {story.status === "success" ? (
                    <span className="flex items-center text-green-600">
                      <Check className="h-4 w-4 mr-1" /> Success
                    </span>
                  ) : story.status === "warning" ? (
                    <span className="flex items-center text-amber-600">
                      <AlertCircle className="h-4 w-4 mr-1" /> Warning
                    </span>
                  ) : (
                    <span className="flex items-center text-red-600">
                      <X className="h-4 w-4 mr-1" /> Error
                    </span>
                  )}
                </TableCell>
                <TableCell>{story.title}</TableCell>
                <TableCell>{story.industry}</TableCell>
                <TableCell>{story.region}</TableCell>
                <TableCell>{story.technology}</TableCell>
                <TableCell>
                  {story.status === "success" ? (
                    <Button variant="ghost" size="sm" asChild>
                      <LinkIcon to={`/admin/edit-story/${story.id}`}>View</LinkIcon>
                    </Button>
                  ) : (
                    <Button variant="ghost" size="sm">Fix</Button>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      <div className="flex justify-between items-center">
        <Button variant="outline">Return to Dashboard</Button>
        <Button>
          View Repository <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </div>
    </div>
  );
};

// Component for the review step
const ReviewDataView = ({ data, onConfirm }: { data: any[], onConfirm: () => void }) => {
  const [showAiHelp, setShowAiHelp] = useState(false);
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Review & Validate</h3>
          <p className="text-sm text-muted-foreground">
            Review the mapped data before uploading to the system
          </p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          className="flex items-center gap-1"
          onClick={() => setShowAiHelp(!showAiHelp)}
        >
          <Sparkles className="h-4 w-4" /> AI Assistance
        </Button>
      </div>

      {showAiHelp && (
        <Card className="bg-mobio-lavender bg-opacity-20 border-mobio-blue">
          <CardContent className="p-4">
            <div className="flex items-start gap-2">
              <Sparkles className="h-5 w-5 text-mobio-blue mt-1" />
              <div>
                <h4 className="font-medium text-mobio-blue">AI Analysis Insights</h4>
                <ul className="list-disc pl-5 space-y-1 mt-2 text-sm">
                  <li>2 stories are missing required technology tags</li>
                  <li>1 story has a potentially incorrect region (suggests "Global" instead of "Asia")</li>
                  <li>3 stories could benefit from additional business outcome tags</li>
                  <li>Several duplicates detected - check story IDs #3 and #6</li>
                </ul>
                <div className="mt-2 flex justify-end">
                  <Button size="sm" variant="outline">Apply AI Suggestions</Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <ScrollArea className="h-[400px] rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Industry</TableHead>
                <TableHead>Region</TableHead>
                <TableHead>Technology</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((story) => (
                <TableRow key={story.id}>
                  <TableCell className="font-medium">{story.title}</TableCell>
                  <TableCell>{story.industry}</TableCell>
                  <TableCell>{story.region}</TableCell>
                  <TableCell>{story.technology}</TableCell>
                  <TableCell>
                    {story.status === "success" ? (
                      <span className="flex items-center text-green-600">
                        <Check className="h-4 w-4 mr-1" /> Valid
                      </span>
                    ) : story.status === "warning" ? (
                      <span className="flex items-center text-amber-600">
                        <AlertCircle className="h-4 w-4 mr-1" /> Warning
                      </span>
                    ) : (
                      <span className="flex items-center text-red-600">
                        <X className="h-4 w-4 mr-1" /> Error
                      </span>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
      </Card>

      <div className="flex justify-between items-center pt-4">
        <div className="text-sm text-muted-foreground">
          <span className="font-medium">{data.filter(d => d.status === "success").length}</span> of {data.length} stories ready for upload
        </div>
        <div className="flex gap-2">
          <Button variant="outline">Back</Button>
          <Button onClick={onConfirm}>Upload Stories</Button>
        </div>
      </div>
    </div>
  );
};

// Component for field mapping
const FieldMappingView = ({ fields, onContinue }: { fields: any[], onContinue: () => void }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Map Fields</h3>
        <p className="text-sm text-muted-foreground">
          Map Excel columns to system fields
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardContent className="p-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Excel Column</TableHead>
                  <TableHead>System Field</TableHead>
                  <TableHead>Required</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {fields.map((field, index) => (
                  <TableRow key={index}>
                    <TableCell>{field.excelField}</TableCell>
                    <TableCell>
                      <Select defaultValue={field.systemField}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select field" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="title">Title</SelectItem>
                          <SelectItem value="industry">Industry</SelectItem>
                          <SelectItem value="region">Region</SelectItem>
                          <SelectItem value="technology">Technology</SelectItem>
                          <SelectItem value="summary">Summary</SelectItem>
                          <SelectItem value="businessProblem">Business Problem</SelectItem>
                          <SelectItem value="outcomes">Outcomes</SelectItem>
                          <SelectItem value="date">Date</SelectItem>
                          <SelectItem value="clientSize">Client Size</SelectItem>
                          <SelectItem value="salesLead">Sales Lead</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      {field.required ? (
                        <span className="text-red-500">Yes</span>
                      ) : (
                        <span>No</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {field.mapped ? (
                        <Check className="h-5 w-5 text-green-500" />
                      ) : (
                        <X className="h-5 w-5 text-red-500" />
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <h4 className="text-sm font-medium mb-2">Advanced Options</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date-format">Date Format</Label>
                <Select defaultValue="mm-dd-yyyy">
                  <SelectTrigger id="date-format">
                    <SelectValue placeholder="Select date format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mm-dd-yyyy">MM-DD-YYYY</SelectItem>
                    <SelectItem value="dd-mm-yyyy">DD-MM-YYYY</SelectItem>
                    <SelectItem value="yyyy-mm-dd">YYYY-MM-DD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="text-format">Text Format</Label>
                <Select defaultValue="plain">
                  <SelectTrigger id="text-format">
                    <SelectValue placeholder="Select text format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="plain">Plain Text</SelectItem>
                    <SelectItem value="rich">Rich Text</SelectItem>
                    <SelectItem value="markdown">Markdown</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between items-center pt-4">
        <div className="text-sm text-muted-foreground">
          <span className="font-medium">{fields.filter(f => f.mapped).length}</span> of {fields.length} fields mapped
        </div>
        <div className="flex gap-2">
          <Button variant="outline">Back</Button>
          <Button onClick={onContinue}>Continue</Button>
        </div>
      </div>
    </div>
  );
};

// Component for upload files
const UploadFilesView = ({ onFileUploaded }: { onFileUploaded: () => void }) => {
  const [activeTab, setActiveTab] = useState("excel");
  const [dragging, setDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(true);
  };
  
  const handleDragLeave = () => {
    setDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);
    // Mock file upload
    handleFileUpload();
  };
  
  const handleFileUpload = () => {
    setShowUploadDialog(true);
    setIsUploading(true);
    
    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setUploadProgress(progress);
      
      if (progress >= 100) {
        clearInterval(interval);
        setIsUploading(false);
        setTimeout(() => {
          setShowUploadDialog(false);
          onFileUploaded();
        }, 500);
      }
    }, 300);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Upload Files</h3>
        <p className="text-sm text-muted-foreground">
          Upload your Excel spreadsheet with success story data
        </p>
      </div>
      
      <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="excel">Excel Upload</TabsTrigger>
          <TabsTrigger value="manual">Manual Entry</TabsTrigger>
        </TabsList>
        
        <TabsContent value="excel" className="pt-4">
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center ${
              dragging ? "border-primary bg-primary/5" : ""
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="flex flex-col items-center justify-center gap-2">
              <div className="p-3 rounded-full bg-primary/10">
                <FileSpreadsheet className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-lg font-medium">Upload Excel File</h3>
              <p className="text-sm text-muted-foreground max-w-sm mx-auto">
                Drag and drop your Excel file here, or click to browse. The file should include story titles, 
                descriptions, and metadata.
              </p>
              <div className="mt-4">
                <Input
                  id="file-upload"
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  className="hidden"
                  onChange={handleFileUpload}
                />
                <Button asChild>
                  <label htmlFor="file-upload" className="cursor-pointer flex items-center gap-1">
                    <UploadCloud className="h-4 w-4" /> Select File
                  </label>
                </Button>
              </div>
            </div>
          </div>
          
          <div className="mt-6 space-y-2">
            <h4 className="text-sm font-medium">Template & Instructions</h4>
            <p className="text-sm text-muted-foreground">
              Download our template for the correct format, or view the instructions to prepare your data.
            </p>
            <div className="flex flex-wrap gap-2 mt-2">
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <FileSpreadsheet className="h-4 w-4" /> Download Template
              </Button>
              <Button variant="outline" size="sm">View Instructions</Button>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <ExternalLink className="h-4 w-4" /> Open Sample
              </Button>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="manual" className="pt-4">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="story-title">Success Story Title</Label>
                <Input id="story-title" placeholder="Enter story title" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="story-industry">Industry</Label>
                <Select>
                  <SelectTrigger id="story-industry">
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="retail">Retail</SelectItem>
                    <SelectItem value="healthcare">Healthcare</SelectItem>
                    <SelectItem value="finance">Financial Services</SelectItem>
                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="story-region">Region</Label>
                <Select>
                  <SelectTrigger id="story-region">
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="na">North America</SelectItem>
                    <SelectItem value="europe">Europe</SelectItem>
                    <SelectItem value="apac">APAC</SelectItem>
                    <SelectItem value="global">Global</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="story-technology">Technology</Label>
                <Select>
                  <SelectTrigger id="story-technology">
                    <SelectValue placeholder="Select technology" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ai">AI/ML</SelectItem>
                    <SelectItem value="cloud">Cloud Solutions</SelectItem>
                    <SelectItem value="iot">IoT</SelectItem>
                    <SelectItem value="mobile">Mobile</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="story-summary">Summary</Label>
              <Textarea id="story-summary" placeholder="Enter a brief summary of the success story" />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="story-description">Full Description</Label>
              <Textarea id="story-description" placeholder="Enter the full success story description" className="min-h-[150px]" />
            </div>
            
            <div className="pt-4 flex justify-end">
              <Button onClick={() => onFileUploaded()}>Continue</Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Upload Progress Dialog */}
      <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Uploading File</DialogTitle>
            <DialogDescription>
              {isUploading ? "Uploading and processing your Excel file..." : "Upload complete!"}
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <Progress value={uploadProgress} className="h-2" />
            <p className="text-sm text-right mt-2">
              {isUploading ? `${uploadProgress}% complete` : "Processing complete"}
            </p>
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              disabled={isUploading}
              onClick={() => {
                setShowUploadDialog(false);
                if (!isUploading) onFileUploaded();
              }}
            >
              {isUploading ? "Uploading..." : "Continue"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Steps component
const Steps = ({ steps, currentStep }: { steps: typeof stepsData, currentStep: number }) => {
  return (
    <div className="relative mb-8">
      <div className="absolute inset-0 flex items-center">
        <div className="w-full border-t border-border"></div>
      </div>
      <div className="relative flex justify-between">
        {steps.map((step) => (
          <div key={step.id} className="flex flex-col items-center">
            <div 
              className={`h-8 w-8 rounded-full flex items-center justify-center ${
                step.id < currentStep 
                  ? "bg-primary text-primary-foreground" 
                  : step.id === currentStep 
                    ? "bg-primary text-primary-foreground" 
                    : "border border-border bg-background"
              }`}
            >
              {step.id < currentStep ? (
                <Check className="h-5 w-5" />
              ) : (
                step.id
              )}
            </div>
            <div className="text-xs text-center mt-1">{step.name}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

const BulkUpload = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const navigate = useNavigate();
  
  const handleFileUploaded = () => {
    setCurrentStep(2);
  };
  
  const handleFieldsMapped = () => {
    setCurrentStep(3);
  };
  
  const handleReviewCompleted = () => {
    setCurrentStep(4);
    toast({
      title: "Upload Started",
      description: "Your stories are being uploaded to the system",
    });
  };
  
  return (
    <div className="max-w-5xl mx-auto">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold">Bulk Upload</h1>
          <p className="text-muted-foreground">
            Upload and manage multiple success stories in one go
          </p>
        </div>
        <Button variant="outline" onClick={() => navigate('/admin')}>
          Cancel
        </Button>
      </div>
      
      <Steps steps={stepsData} currentStep={currentStep} />
      
      <div className="mt-8">
        {currentStep === 1 && <UploadFilesView onFileUploaded={handleFileUploaded} />}
        {currentStep === 2 && <FieldMappingView fields={fieldMappingsMockData} onContinue={handleFieldsMapped} />}
        {currentStep === 3 && <ReviewDataView data={storiesMockData} onConfirm={handleReviewCompleted} />}
        {currentStep === 4 && <UploadResultsView results={storiesMockData} />}
      </div>
    </div>
  );
};

export default BulkUpload;
