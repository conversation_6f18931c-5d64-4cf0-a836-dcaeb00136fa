import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";
import { errorHandler } from "./middleware/errorHandler.js";
import { auditLogger } from "./middleware/auditLogger.js";
import { logger } from "./utils/logger.js";
import { initializeDatabase } from "./utils/database.js";
import { setupGracefulShutdown } from "./utils/server.js";
import mainRoutes from "./routes/index.js";

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const app = express();

const setupMiddleware = (app) => {
  app.use(cors());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  app.use("/uploads", express.static(path.join(__dirname, "uploads")));
  app.use(auditLogger);
  logger.info("✅ Middleware setup completed");
};

const setupRoutes = (app) => {
  app.use("/api", mainRoutes);
  app.use(errorHandler);

  app.use((_req, res) => {
    logger.warn(`Route not found: ${_req.originalUrl}`);
    res.status(404).json({
      success: false,
      message: "Route not found",
    });
  });

  logger.info("✅ Routes registered successfully");
};

const setupErrorHandlers = () => {
  process.on("unhandledRejection", (err) => {
    logger.error("🚫 Unhandled Promise Rejection:", err);
  });

  process.on("uncaughtException", (err) => {
    logger.error("🚫 Uncaught Exception:", err);
    process.exit(1);
  });

  logger.info("✅ Error handlers setup completed");
};

const startServer = async () => {
  logger.info("🔄 Initializing database...");
  await initializeDatabase();
  logger.info("✅ Database initialized successfully");

  setupMiddleware(app);
  setupRoutes(app);
  setupErrorHandlers();

  const PORT = process.env.PORT || 5000;
  const server = app.listen(PORT, () => {
    logger.info(`
🚀 Server is running!
📡 Port: ${PORT}
🌐 API Endpoint: http://localhost:${PORT}/api
    `);
  });

  setupGracefulShutdown(server);
  return app;
};

startServer().catch((error) => {
  logger.error("🚫 Server startup failed:", error);
  process.exit(1);
});

export default app;
