
import { NavLink, useLocation, Link } from "react-router-dom";
import {
  Home,
  Database,
  FolderPlus,
  Upload,
  Lightbulb,
  Archive,
  ChevronLeft,
  LogOut
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";

const AppSidebar = () => {
  const sidebar = useSidebar();
  const location = useLocation();
  const currentPath = location.pathname;
  const { logout, isAdmin } = useAuth();

  const menuItems = [
    { title: "Dashboard", path: "/admin", icon: Home },
    { title: "Master Data Manager", path: "/admin/master-data", icon: Database },
    { title: "Add Success Story", path: "/admin/add-story", icon: FolderPlus },
    { title: "Story Repository", path: "/admin/repository", icon: Archive },
    // Hidden temporarily as requested:
    { title: "Bulk Upload", path: "/admin/bulk-upload", icon: Upload },
    { title: "Admin Intelligence", path: "/admin/intelligence", icon: Lightbulb },
  ];

  const isActive = (path: string) => {
    if (path === '/admin') {
      return currentPath === path;
    }
    return currentPath.startsWith(path);
  };

  return (
    <Sidebar
      className={`transition-all duration-300 ease-in-out border-r ${
        sidebar.state === "collapsed" ? "w-16" : "w-64"
      } hidden md:block`}
      collapsible="icon"
    >
      <SidebarHeader className="h-16 flex items-center justify-center border-b py-4 px-2">
        {sidebar.state !== "collapsed" ? (
          <div className="flex items-center space-x-2 hover-transition hover:scale-105">
            <img src="/lovable-uploads/01aba598-b6ed-4261-93a5-04e01262ab6e.png" alt="Mobio Logo" className="h-8" />
            <span className="font-poppins font-semibold text-mobio-blue">WinWise</span>
          </div>
        ) : (
          <img 
            src="/lovable-uploads/01aba598-b6ed-4261-93a5-04e01262ab6e.png" 
            alt="Mobio Logo" 
            className="h-8 hover-transition hover:scale-110"
          />
        )}
      </SidebarHeader>

      <SidebarContent className="px-2 py-4">
        <SidebarMenu>
          {menuItems.map((item) => (
            <SidebarMenuItem key={item.path}>
              <SidebarMenuButton asChild>
                <NavLink
                  to={item.path}
                  className={({ isActive }) =>
                    `flex items-center py-2 px-3 my-1 rounded-md transition-all duration-200 ${
                      isActive
                        ? "bg-mobio-lightblue/20 text-mobio-blue font-medium hover:bg-mobio-lightblue/30 shadow-sm"
                        : "hover:bg-secondary hover:translate-x-1"
                    }`
                  }
                >
                  <item.icon className={`h-5 w-5 ${isActive(item.path) ? "text-mobio-blue" : ""}`} />
                  {sidebar.state !== "collapsed" && 
                    <span className={`ml-3 ${isActive(item.path) ? "font-medium" : ""}`}>
                      {item.title}
                    </span>
                  }
                  {isActive(item.path) && (
                    <span className="absolute left-0 w-1 h-4/5 bg-mobio-blue rounded-r-full"></span>
                  )}
                </NavLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}

          {/* Go to user interface option */}
          {isAdmin && (
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <Link
                  to="/"
                  className="flex items-center py-2 px-3 my-1 rounded-md transition-all duration-200
                   mt-4 border border-dashed border-muted-foreground/30 hover:bg-secondary"
                >
                  <Home className="h-5 w-5 text-muted-foreground" />
                  {sidebar.state !== "collapsed" && 
                    <span className="ml-3 text-muted-foreground">
                      User Interface
                    </span>
                  }
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className="p-2 border-t">
        <div className="flex flex-col space-y-2">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start text-muted-foreground hover:text-red-500"
            onClick={logout}
          >
            <LogOut className="h-4 w-4 mr-2" />
            {sidebar.state !== "collapsed" && <span>Log out</span>}
          </Button>
          
          <SidebarTrigger>
            <button className="w-full flex items-center justify-center p-2 rounded-md hover:bg-secondary btn-hover">
              <ChevronLeft 
                className={`h-5 w-5 transition-transform duration-300 ${
                  sidebar.state === "collapsed" ? 'rotate-180' : ''
                }`} 
              />
              {sidebar.state !== "collapsed" && <span className="ml-2">Collapse</span>}
            </button>
          </SidebarTrigger>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
};

export default AppSidebar;
