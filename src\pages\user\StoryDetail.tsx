import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
} from "lucide-react";
import { toast } from "sonner";
import { useStoryDetails } from "@/hooks/useStoryDetails";
import { useTags } from "@/hooks/useTags";
import StoryDetailHeader from "./story-detail-components/StoryDetailHeader";
import StoryMediaSection, { MediaType as ImportedMediaType } from "./story-detail-components/StoryMediaSection";
import StoryImplementationDetails from "./story-detail-components/StoryImplementationDetails";
import LoadingIndicator from "./story-detail-components/LoadingIndicator"; // Import LoadingIndicator
import ErrorDisplay from "./story-detail-components/ErrorDisplay"; // Import ErrorDisplay


const StoryDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { story, isLoading, error } = useStoryDetails(id);
  const [isSaved, setIsSaved] = useState(false);
  const [activeMediaTab, setActiveMediaTab] = useState<ImportedMediaType>("images");

  // Add these hooks for each category type
  const { tags: industryTags } = useTags(story?.categories?.industry || []);
  const { tags: businessProblemTags } = useTags(
    story?.categories?.businessProblem || []
  );
  const { tags: technologyTags } = useTags(story?.categories?.technology || []);
  const { tags: outcomeTags } = useTags(story?.categories?.outcome || []);

  const handleBackNavigation = () => {
    navigate(-1); // This will navigate back to the previous page
  };

  const handleSaveToLibrary = () => {
    setIsSaved(!isSaved);
    toast.success(
      isSaved ? "Removed from your library" : "Added to your library"
    );
  };

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    toast.success("Link copied to clipboard");
  };

  const handleDownload = async (item: { url: string; name: string }) => {
    try {
      const response = await fetch(item.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = item.name || "download";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Download failed:", error); // Log the actual error
      toast.error("Failed to download file. Please try again.");
    }
  };

  if (isLoading) {
    return <LoadingIndicator message="Loading story details..." />;
  }

  if (error || !story) {
    return <ErrorDisplay title="Error Loading Story" message={error || "Story not found"} />;
  }

  // activeMedia is now managed within StoryMediaSection
  // getMediaByType and getMediaCount are now managed within StoryMediaSection

  return (
    <div className="max-w-6xl mx-auto">
      {/* Back Navigation */}
      <div className="mb-6">
        <button
          onClick={handleBackNavigation}
          className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
          <span>Back to Stories</span>
        </button>
      </div>

      {/* Header Section */}
      <StoryDetailHeader
        story={story}
        industryTags={industryTags}
        isSaved={isSaved}
        onSaveToLibrary={handleSaveToLibrary}
        onShare={handleShare}
      />

      {/* Content Section */}
      <div className="grid grid-cols-1 gap-8">
        <StoryMediaSection
          media={story?.media}
          activeMediaTab={activeMediaTab}
          onActiveMediaTabChange={setActiveMediaTab}
          onDownloadItem={handleDownload}
        />
        <StoryImplementationDetails
          storyDetails={story}
          industryTags={industryTags}
          businessProblemTags={businessProblemTags}
          technologyTags={technologyTags}
          outcomeTags={outcomeTags}
        />
      </div>
    </div>
  );
};

export default StoryDetail;
