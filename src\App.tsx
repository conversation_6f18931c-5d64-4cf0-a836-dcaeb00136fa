import React from "react";
import { ToastProvider, Toaster } from "@/components/ui/toast"; // Adjust the path if necessary
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AuthProvider } from "@/contexts/AuthContext";

import AdminLayout from "./components/AdminLayout";
import UserLayout from "./components/UserLayout";
import ProtectedRoute from "./components/ProtectedRoute";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import MasterData from "./pages/MasterData";
import AddEditStory from "./pages/AddEditStory";
import Repository from "./pages/Repository";
import BulkUpload from "./pages/BulkUpload";
import Intelligence from "./pages/Intelligence";
import NotFound from "./pages/NotFound";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// User Pages
import Home from "./pages/user/Home";
import SearchResults from "./pages/user/SearchResults";
import StoryDetail from "./pages/user/StoryDetail";
import MyLibrary from "./pages/user/MyLibrary";
import UserSettings from "./pages/user/Settings";
import Register from "./pages/Register";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <ToastProvider>
        <Toaster />
        <BrowserRouter>
          <AuthProvider>
            <SidebarProvider>
              <div className="min-h-screen w-full fade-in">
                <Routes>
                  {/* Auth Routes */}
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />

                  {/* Admin Routes - Protected with admin role requirement */}
                  <Route
                    path="/admin"
                    element={
                      <ProtectedRoute requireAdmin={true}>
                        <AdminLayout />
                      </ProtectedRoute>
                    }
                  >
                    <Route index element={<Dashboard />} />
                    <Route path="master-data" element={<MasterData />} />
                    <Route path="add-story" element={<AddEditStory />} />
                    <Route path="edit-story/:id" element={<AddEditStory />} />
                    <Route path="repository" element={<Repository />} />
                    {/* Hidden temporarily as requested */}
                    {/* <Route path="bulk-upload" element={<BulkUpload />} />
                    <Route path="intelligence" element={<Intelligence />} /> */}
                  </Route>

                  {/* User Routes - Protected but no admin role required */}
                  <Route
                    path="/"
                    element={
                      <ProtectedRoute>
                        <UserLayout />
                      </ProtectedRoute>
                    }
                  >
                    <Route index element={<Home />} />
                    <Route path="search" element={<SearchResults />} />
                    <Route path="story/:id" element={<StoryDetail />} />
                    <Route path="library" element={<MyLibrary />} />
                    <Route path="settings" element={<UserSettings />} />
                  </Route>

                  {/* Redirect root to login if not authenticated */}
                  <Route path="/" element={<Navigate to="/login" replace />} />

                  <Route path="*" element={<NotFound />} />
                </Routes>
                {/* Customized ToastContainer */}
                <ToastContainer
                  position="bottom-right"
                  autoClose={5000}
                  hideProgressBar={false}
                  newestOnTop={false}
                  closeOnClick
                  rtl={false}
                  pauseOnFocusLoss
                  draggable
                  pauseOnHover
                  theme="colored"
                />
              </div>
            </SidebarProvider>
          </AuthProvider>
        </BrowserRouter>
      </ToastProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;