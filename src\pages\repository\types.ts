
export interface Asset {
  id?: string;
  url: string;
  name: string;
  kind?: string;
  mimeType?: string;
  previewUrl: string;
  type?: string;
}

export interface SuccessStory {
  id: string;
  title: string;
  executive_summary: string;
  client_segment: string;
  oneline_summary: string;
  business_challenges: string;
  success_story_detail: string;
  industries: string[];
  regions: string[];
  technologies: string[];
  outcomes: string[];
  tags: string[];
  image_assets: Asset[];
  audio_assets: Asset[];
  video_assets: Asset[];
  document_assets: Asset[];
  demo_assets: Asset[];
  owner?: {
    name: string;
    id: string;
  };
  status: string;
  created_at: string;
  updated_at: string;
}

export interface FilterState {
  industry: string[];
  region: string[];
  technology: string[];
}
