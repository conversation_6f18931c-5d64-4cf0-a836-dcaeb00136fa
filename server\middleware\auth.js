import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

export const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      res.status(401).json({
        success: false,
        message: 'No token provided'
      });
      return;
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error) {
      console.error('Token validation error:', error);
      res.status(401).json({
        success: false,
        message: 'Invalid or expired token',
        details: error.message
      });
      return;
    }

    if (!user) {
      res.status(401).json({
        success: false,
        message: 'User not found in token'
      });
      return;
    }

    // Get additional user data from our users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, role')
      .eq('id', user.id)
      .single();

    if (userError) {
      console.error('User data fetch error:', userError);
      res.status(401).json({
        success: false,
        message: 'User not found in database'
      });
      return;
    }

    req.user = {
      id: userData.id,
      role: userData.role
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    next(error);
  }
};