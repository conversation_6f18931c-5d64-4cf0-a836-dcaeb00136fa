
import { FileText, Image, Music, Video, MonitorPlay } from "lucide-react";

interface AttachmentsProps {
  attachments: {
    image: boolean;
    audio: boolean;
    video: boolean;
    document: boolean;
    demo: boolean;
  };
}

const StoryAttachmentIcons = ({ attachments }: AttachmentsProps) => {
  return (
    <div className="flex gap-2">
      {attachments.document && <FileText className="h-4 w-4 text-muted-foreground" />}
      {attachments.image && <Image className="h-4 w-4 text-muted-foreground" />}
      {attachments.audio && <Music className="h-4 w-4 text-muted-foreground" />}
      {attachments.video && <Video className="h-4 w-4 text-muted-foreground" />}
      {attachments.demo && <MonitorPlay className="h-4 w-4 text-muted-foreground" />}
    </div>
  );
};

export default StoryAttachmentIcons;
