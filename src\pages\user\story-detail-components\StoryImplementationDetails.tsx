import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Cpu, Check } from "lucide-react";

interface Tag {
  id: string;
  name: string;
}

interface StoryDetails {
  businessChallenges: string;
  summary: string;
  successStoryDetail: string;
}

interface StoryImplementationDetailsProps {
  storyDetails: StoryDetails;
  industryTags: Tag[];
  businessProblemTags: Tag[];
  technologyTags: Tag[];
  outcomeTags: Tag[];
}

const StoryImplementationDetails = ({
  storyDetails,
  industryTags,
  businessProblemTags,
  technologyTags,
  outcomeTags,
}: StoryImplementationDetailsProps) => {
  return (
    <Card>
      <div className="bg-slate-50 p-4 border-b">
        <h2 className="text-xl font-medium text-mobio-blue">
          Implementation Journey & Impact
        </h2>
      </div>

      <CardContent className="p-6 space-y-6">
        <div>
          <h3 className="text-lg font-medium mb-2 flex items-center">
            <Cpu className="h-4 w-4 mr-2 text-mobio-blue" />
            Business Challenges
          </h3>
          <p className="text-gray-600">{storyDetails.businessChallenges}</p>

          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-700 mb-1">Industry</h4>
              <div className="flex flex-wrap gap-1">
                {industryTags.map((tag) => (
                  <Badge
                    key={tag.id}
                    variant="outline"
                    className="bg-blue-50"
                  >
                    {tag.name}
                  </Badge>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-700 mb-1">
                Business Problem
              </h4>
              <div className="flex flex-wrap gap-1">
                {businessProblemTags.map((tag) => (
                  <Badge
                    key={tag.id}
                    variant="outline"
                    className="bg-purple-50"
                  >
                    {tag.name}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>

        <Separator />

        <div>
          <h3 className="text-lg font-medium mb-2 flex items-center">
            <Cpu className="h-4 w-4 mr-2 text-mobio-blue" />
            Executive Summary
          </h3>
          <p className="text-gray-600">{storyDetails.summary}</p>

          <div className="mt-4">
            <h4 className="font-medium text-gray-700 mb-1">Technologies</h4>
            <div className="flex flex-wrap gap-1">
              {technologyTags.map((tag) => (
                <Badge
                  key={tag.id}
                  variant="outline"
                  className="bg-green-50"
                >
                  {tag.name}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        <Separator />

        <div>
          <h3 className="text-lg font-medium mb-2 flex items-center">
            <Check className="h-4 w-4 mr-2 text-mobio-blue" />
            Success Story
          </h3>
          <p className="text-gray-600">{storyDetails.successStoryDetail}</p>

          <div className="mt-4">
            <h4 className="font-medium text-gray-700 mb-1">Outcomes</h4>
            <div className="flex flex-wrap gap-1">
              {outcomeTags.map((tag) => (
                <Badge key={tag.id} variant="outline" className="bg-red-50">
                  {tag.name}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StoryImplementationDetails;