
import { Loader2 } from "lucide-react";

interface LoadingIndicatorProps {
  message?: string;
}

export const LoadingIndicator = ({ message = "Loading story data..." }: LoadingIndicatorProps) => {
  return (
    <div className="flex h-[80vh] items-center justify-center">
      <div className="flex flex-col items-center gap-2">
        <Loader2 className="h-8 w-8 animate-spin text-mobio-blue" />
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  );
};
