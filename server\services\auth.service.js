import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// User validation schema
const UserSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  password: z.string().min(6),
  role: z.enum(['admin', 'user']).default('user'),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  phone: z.string().optional(),
  profile_picture_url: z.string().optional(),
  designation: z.string().optional(),
  department: z.string().optional(),
  location: z.string().optional(),
  bio: z.string().optional(),
  flag_active: z.boolean().default(true).optional()
});

// Initialize Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

export class AuthService {
  async login(email, password) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) throw error;

      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, name, role')
        .eq('email', email)
        .single();

      if (userError) throw userError;

      return {
        user: {
          ...userData,
          email: data.user?.email
        },
        session: data.session
      };
    } catch (error) {
      throw error;
    }
  }

  async register(userData) {
    try {
      // Validate user data first
      const validatedData = UserSchema.parse({
        name: userData.name,
        email: userData.email,
        password: userData.password,
        role: userData.role || 'user',
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone: userData.phone,
        profile_picture_url: userData.profile_picture_url,
        designation: userData.designation,
        department: userData.department,
        location: userData.location,
        bio: userData.bio,
        flag_active: userData.flag_active !== undefined ? userData.flag_active : true
      });

      // First check if user exists in database
      const { data: existingUser } = await supabaseAdmin
        .from('users')
        .select('id')
        .eq('email', validatedData.email)
        .single();

      if (existingUser) {
        throw new Error('User already exists in database');
      }

      // Create auth user with admin client and auto-confirm
      const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
        email: validatedData.email,
        password: validatedData.password,
        email_confirm: true,
        user_metadata: {
          name: validatedData.name,
          role: validatedData.role
        }
      });

      if (authError) {
        console.error('Auth Error Details:', {
          message: authError.message,
          status: authError.status,
          code: authError.code
        });
        throw authError;
      }

      if (!authData.user) {
        throw new Error('Failed to create authentication user');
      }

      // Create database user using admin client with all profile fields
      const { data: dbUser, error: dbError } = await supabaseAdmin
        .from('users')
        .insert({
          id: authData.user.id,
          name: validatedData.name,
          email: validatedData.email,
          role: validatedData.role || 'user',
          first_name: validatedData.first_name,
          last_name: validatedData.last_name,
          phone: validatedData.phone,
          profile_picture_url: validatedData.profile_picture_url,
          designation: validatedData.designation,
          department: validatedData.department,
          location: validatedData.location,
          bio: validatedData.bio,
          flag_active: validatedData.flag_active
        })
        .select()
        .single();

      if (dbError) {
        console.error('Database Error:', dbError);
        throw dbError;
      }

      return {
        user: dbUser,
        session: authData.session
      };
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  async getProfile(userId) {
    try {
      const { data: profile, error } = await supabase
        .from('users')
        .select('id, name, email, role, created_at')
        .eq('id', userId)
        .single();

      if (error) throw error;

      return profile;
    } catch (error) {
      throw error;
    }
  }

  async getAllUsers() {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .select('id, name, email, role, created_at, updated_at, flag_active')
        .eq('flag_deleted', false);

      if (error) throw error;
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getUserById(userId) {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .select('id, name, email, role, created_at, updated_at, flag_active')
        .eq('id', userId)
        .eq('flag_deleted', false)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw error;
    }
  }

  async updateUser(userId, updateData) {
    try {
      const validatedData = z.object({
        name: z.string().min(2).optional(),
        email: z.string().email().optional(),
        role: z.enum(['admin', 'user']).optional(),
        flag_active: z.boolean().optional()
      }).parse(updateData);

      const { data, error } = await supabaseAdmin
        .from('users')
        .update({
          ...validatedData,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .eq('flag_deleted', false)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw error;
    }
  }

  async deleteUser(userId, currentUserId) {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .update({
          flag_deleted: true,
          updated_at: new Date().toISOString(),
          updated_by: currentUserId
        })
        .eq('id', userId)
        .eq('flag_deleted', false)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw error;
    }
  }

  async logout(userId) {
    try {
      // Use Supabase Admin SDK to sign out the user from all their sessions.
      // This effectively invalidates their current access token and refresh tokens from the server's perspective.
      const { error } = await supabaseAdmin.auth.admin.signOut(userId);

      if (error) {
        console.error(`Supabase admin signout error for user ${userId}:`, error.message);
        // Even if Supabase call has an issue, the client will clear its token.
        // Depending on the error, you might want to throw or handle differently.
        // For critical errors (e.g., service unavailable), throwing might be appropriate.
        // For user-specific errors (e.g., user not found, though unlikely here if token was valid),
        // it might indicate an inconsistent state.
        throw new Error(`Failed to sign out user from server: ${error.message}`);
      }

      return { message: "User signed out successfully from all server sessions." };
    } catch (error) {
      // Log the original error if it's not the one we threw
      if (!(error.message.startsWith("Failed to sign out user from server:"))) {
        console.error('Logout service error:', error);
      }
      throw error; // Re-throw to be caught by the controller
    }
  }

  async refreshToken(clientRefreshToken) {
    if (!clientRefreshToken) {
      throw new Error("Refresh token is required.");
    }
    try {
      // Use the standard Supabase client (not admin) for refreshing the session,
      // as this is an operation typically initiated by a user's client.
      const { data, error } = await supabase.auth.refreshSession({
        refresh_token: clientRefreshToken
      });

      if (error) {
        console.error('Supabase session refresh error:', error.message);
        throw new Error(`Failed to refresh session: ${error.message}`);
      }

      if (!data.session || !data.user) {
        // This might happen if the refresh token is invalid or revoked by Supabase
        console.warn('Refresh token did not yield a new session or user.');
        throw new Error('Invalid refresh token or session could not be established.');
      }
      
      // Optionally, you might want to fetch additional user details from your 'users' table
      // if the data.user from Supabase auth is not sufficient.
      // For now, we'll assume data.user and data.session are what's needed.
      // Re-fetch user details from 'users' table to ensure consistency, especially for 'role'
      const { data: userDataFromDb, error: userDbError } = await supabase
        .from('users')
        .select('id, name, role, email') // Ensure email is selected if needed
        .eq('id', data.user.id)
        .single();

      if (userDbError) {
        console.error('Error fetching user details after session refresh:', userDbError.message);
        throw new Error('Failed to fetch user details after session refresh.');
      }
      
      if (!userDataFromDb) {
        throw new Error('User not found in database after session refresh.');
      }

      return {
        user: { // Construct user object similar to login
          id: userDataFromDb.id,
          name: userDataFromDb.name,
          email: data.user.email, // email from supabase auth user
          role: userDataFromDb.role,
        },
        session: data.session, // Contains new access_token and potentially new refresh_token
      };
    } catch (error) {
      if (!(error.message.startsWith("Failed to refresh session:")) &&
          !(error.message.startsWith("Refresh token is required.")) &&
          !(error.message.startsWith("Invalid refresh token or session could not be established.")) &&
          !(error.message.startsWith("Failed to fetch user details after session refresh.")) &&
          !(error.message.startsWith("User not found in database after session refresh."))) {
        console.error('Refresh token service error:', error);
      }
      throw error; // Re-throw to be caught by the controller
    }
  }

  async updateProfile(userId, profileData) {
    try {
      const validatedData = z.object({
        first_name: z.string().optional(),
        last_name: z.string().optional(),
        name: z.string().min(2).optional(),
        phone: z.string().optional(),
        profile_picture_url: z.string().optional(),
        designation: z.string().optional(),
        department: z.string().optional(),
        location: z.string().optional(),
        bio: z.string().optional()
      }).parse(profileData);

      const { data, error } = await supabaseAdmin
        .from('users')
        .update({
          ...validatedData,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .eq('flag_deleted', false)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw error;
    }
  }
}

export default new AuthService();



