import { useState, useEffect, useMemo } from "react";
import axios from "axios"; // Keep for AxiosError type checking
import { apiClient } from "./useApi"; // Import the configured apiClient

interface Tag {
  id: string;
  name: string;
  status: boolean;
  usedIn?: number;
  type: string;
}

interface TagGroup {
  type: string;
  tags: Tag[];
}

interface UseGroupedTagsReturn {
  data: TagGroup[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

export const useGroupedTags = (): UseGroupedTagsReturn => {
  const [data, setData] = useState<TagGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Memoize the API URL to prevent unnecessary re-renders
  const apiUrl = useMemo(() => 
    `${import.meta.env.VITE_API_URL}/api/tags/grouped`, 
    []
  );

  const fetchTags = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Use the apiClient which has the interceptor for adding the token
      const response = await apiClient.get(apiUrl, {
        timeout: 5000, // Keep timeout
        validateStatus: (status) => status >= 200 && status < 300 // Keep custom validation
      });

      if (response.data.success) {
        setData(response.data.data);
      } else {
        throw new Error(response.data.message || "Failed to fetch tags");
      }
    } catch (err) {
      let errorMessage = "An error occurred while fetching tags";

      if (axios.isAxiosError(err)) {
        if (err.code === "ECONNABORTED") {
          errorMessage = "Request timeout - please try again";
        } else if (err.response?.status === 401) {
          errorMessage = "Authentication failed - please login again";
        } else if (err.response?.status === 404) {
          errorMessage = "Tag service not available - please check API endpoint";
        } else {
          errorMessage = err.response?.data?.message || err.message;
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }

      const error = new Error(errorMessage);
      setError(error);
      console.error("Error fetching tags:", {
        message: errorMessage,
        originalError: err,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Memoize the refetch function
  const refetch = useMemo(() => fetchTags, [apiUrl]);

  useEffect(() => {
    fetchTags();
  }, [apiUrl]);

  // Memoize the return value to prevent unnecessary re-renders
  return useMemo(() => ({
    data,
    isLoading,
    error,
    refetch
  }), [data, isLoading, error, refetch]);
};
