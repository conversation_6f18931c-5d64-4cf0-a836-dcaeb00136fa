
import { Outlet } from "react-router-dom";
import AppSidebar from "./AppSidebar";
import Header from "./Header";
import { Suspense } from "react";
import { Loader2 } from "lucide-react";

const AdminLayout = () => {
  return (
    <div className="min-h-screen w-full flex flex-col md:flex-row bg-background overflow-hidden">
      <AppSidebar />
      <div className="flex flex-col flex-1 w-full">
        <Header />
        <main className="flex-1 p-3 sm:p-4 md:p-6 overflow-auto">
          <Suspense fallback={
            <div className="flex h-[80vh] items-center justify-center">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-8 w-8 animate-spin text-mobio-blue" />
                <p className="text-sm text-muted-foreground animate-pulse-soft">Loading content...</p>
              </div>
            </div>
          }>
            <div className="animate-fade-in">
              <Outlet />
            </div>
          </Suspense>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
