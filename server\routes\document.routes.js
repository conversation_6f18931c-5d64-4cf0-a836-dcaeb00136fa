import express from "express";
import { uploadConfig, handleUploadError } from "../middleware/fileUpload.js";
import { authenticateToken } from "../middleware/auth.js";
import documentController from "../controller/document.controller.js";

const router = express.Router();

router.post("/process", 
  authenticateToken,
  uploadConfig.single("document"),
  handleUploadError,
  documentController.processContent
);

export default router;
