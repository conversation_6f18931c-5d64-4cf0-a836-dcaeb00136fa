import { useState, useEffect } from "react";

interface StoryMedia {
  id?: string;
  name?: string;
  type?: string;
  url?: string;
  previewUrl?: string;
}

interface DemoMedia extends StoryMedia {
  credentials: Array<{
    role: string;
    email: string;
    password: string;
  }>;
}

interface APIResponse {
  message: string;
  data: {
    id: string;
    title: string;
    executive_summary: string;
    client_segment: string;
    oneline_summary: string;
    business_challenges: string;
    success_story_detail: string;
    industries: string[];
    regions: string[];
    technologies: string[];
    outcomes: string[];
    general_tags: string[];
    image_assets: any[];
    audio_assets: any[];
    video_assets: any[];
    document_assets: any[];
    demo_assets: any[];
    owner_id: string;
    status: string;
    created_at: string;
    updated_at: string;
    created_by: string;
    updated_by: string;
    flag_deleted: boolean;
    owner: {
      name: string;
    };
    creator: {
      name: string;
    };
    updater: {
      name: string;
    };
    owner_name: string;
    creator_name: string;
    updater_name: string;
  };
  status: number;
}

interface Story {
  id: string;
  title: string;
  customer: string;
  clientSegment: string;
  businessChallenges: string;
  confidential: boolean;
  publishDate: string;
  summary: string;
  oneLineSummary: string;
  successStoryDetail: string;
  categories: {
    industry: string[];
    technology: string[];
    businessProblem: string[];
    region: string[];
    outcome: string[];
    general_tag: string[]; 
  };
  context: {
    background: string;
    challenge: string;
    solution: string;
    benefits: string;
    results: string;
  };
  media: {
    images: StoryMedia[];
    videos: StoryMedia[];
    audio: StoryMedia[];
    documents: StoryMedia[];
    demo?: DemoMedia[];
  };
  views: number;
  created: string;
  updated: string;
  owner: string;
  creator: string;
  updater: string;
  created_by: string;  // Add this line
  updated_by: string;  // Add this line
  status: string;
  flagDeleted: boolean;
}

export const useStoryDetails = (storyId: string) => {
  const [story, setStory] = useState<Story | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStoryDetails = async () => {
      try {
        setIsLoading(true);
        const token = localStorage.getItem('winwise_token');
        const response = await fetch(
          `${import.meta.env.VITE_API_URL}/api/stories/${storyId}`,
          {
            headers: {
              'Content-Type': 'application/json',
              ...(token && { 'Authorization': `Bearer ${token}` })
            }
          }
        );

        if (!response.ok) {
          console.error('Error fetching story details, status:', response.status);
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || `Failed to fetch story details with status ${response.status}`);
          throw new Error("Failed to fetch story details");
        }

        const apiResponse: APIResponse = await response.json();
        console.log(apiResponse.data.demo_assets);
        console.log({apiResponse})
        // Transform the API response to match our frontend structure
        const transformedStory: Story = {
          id: apiResponse.data.id,
          title: apiResponse.data.title,
          customer: apiResponse.data.owner.name,
          clientSegment: apiResponse.data.client_segment,
          businessChallenges: apiResponse.data.business_challenges,
          confidential: false,
          publishDate: apiResponse.data.created_at,
          summary: apiResponse.data.executive_summary,
          oneLineSummary: apiResponse.data.oneline_summary,
          successStoryDetail: apiResponse.data.success_story_detail,
          categories: {
            industry: apiResponse.data.industries,
            technology: apiResponse.data.technologies,
            businessProblem: apiResponse.data.industries,
            region: apiResponse.data.regions,
            outcome: apiResponse.data.outcomes,
            general_tag: apiResponse.data.general_tags,
          },
          context: {
            background: apiResponse.data.oneline_summary,
            challenge: apiResponse.data.business_challenges,
            solution: apiResponse.data.success_story_detail,
            benefits: apiResponse.data.success_story_detail
              .split("Key outcomes")[0]
              .trim(),
            results: apiResponse.data.success_story_detail.includes(
              "Key outcomes"
            )
              ? `Key outcomes included ${
                  apiResponse.data.success_story_detail.split(
                    "Key outcomes included"
                  )[1]
                }`.trim()
              : "",
          },

          media: {
            images: apiResponse.data.image_assets.map((asset) => ({
              id: asset.id || "",
              name: asset.name || "",
              type: asset.type === "url" ? "url" : "image", // Handle 'url' type
              previewUrl: asset.previewUrl || "",
              url: asset.url || "",
            })),
            videos: apiResponse.data.video_assets.map((asset) => ({
              id: asset.id || "",
              name: asset.name || "",
              type: asset.type === "url" ? "url" : "video", // Handle 'url' type
              previewUrl: asset.previewUrl || "",
              url: asset.url || "",
            })),
            audio: apiResponse.data.audio_assets.map((asset) => ({
              id: asset.id || "",
              name: asset.name || "",
              type: asset.type === "url" ? "url" : "audio", // Handle 'url' type
              previewUrl: asset.previewUrl || "",
              url: asset.url || "",
            })),
            documents: apiResponse.data.document_assets.map((asset) => ({
              id: asset.id || "",
              name: asset.name || "",
              type: asset.type === "url" ? "url" : "document", // Handle 'url' type
              previewUrl: asset.previewUrl || "",
              url: asset.url || "",
            })),
            demo: apiResponse.data.demo_assets.map((asset) => ({
              name: asset.name || "",
              type: asset.type || "url",
              previewUrl: asset.previewUrl || "",
              url: asset.url || "",
              credentials: Array.isArray(asset.credentials)
                ? asset.credentials.map((cred) => ({
                    role: cred.role || "",
                    email: cred.email || "",
                    password: cred.password || "",
                  }))
                : [],
            })),
          },
          views: 0,
          created: apiResponse.data.created_at,
          updated: apiResponse.data.updated_at,
          created_by: apiResponse.data.created_by, // Add this line
          updated_by: apiResponse.data.updated_by, // Add this line
          owner: apiResponse.data.owner_name,
          creator: apiResponse.data.creator_name,
          updater: apiResponse.data.updater_name,
          status: apiResponse.data.status,
          flagDeleted: apiResponse.data.flag_deleted,
        };

        setStory(transformedStory);
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      } finally {
        setIsLoading(false);
      }
    };

    if (storyId) {
      fetchStoryDetails();
    }
  }, [storyId]);

  return { story, isLoading, error };
};  