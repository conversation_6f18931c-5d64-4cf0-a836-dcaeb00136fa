import { useState, useMemo } from "react";
import { toast } from "@/components/ui/use-toast";
import RepositoryHeader from "./repository/RepositoryHeader";
import SearchBar from "./repository/SearchBar";
import FilterSection from "./repository/FilterSection";
import SuccessStoriesTable from "./repository/SuccessStoriesTable";
import RepositoryPagination from "./repository/RepositoryPagination";
import { useAllStories } from "@/hooks/useAdminStories";
import { useGroupedTags } from "@/hooks/useGroupedTags";
import { FilterState } from "./repository/types";

const Repository = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<FilterState>({
    industry: [],
    region: [],
    technology: [],
  });
  
  const { 
    stories, 
    loading, 
    error, 
    totalPages, 
    currentPage, 
    setCurrentPage 
  } = useAllStories();
  
  const { data: tagGroups } = useGroupedTags();

  // Helper function to get tag name from ID
  const getTagName = (type: string, id: string): string => {
    const group = tagGroups?.find((g) => g.type === type);
    const tag = group?.tags.find((t) => t.id === id);
    return tag?.name || id;
  };

  const filteredStories = useMemo(() => {
    return stories.filter((story) => {
      // Search term filtering
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          story.title.toLowerCase().includes(searchLower) ||
          story.industries.some((industry) =>
            industry.toLowerCase().includes(searchLower)
          ) ||
          story.technologies.some((tech) =>
            tech.toLowerCase().includes(searchLower)
          );
        if (!matchesSearch) return false;
      }

      // Filter by industry
      if (filters.industry.length > 0) {
        const industryNames = filters.industry.map((id) =>
          getTagName("industry", id).toLowerCase()
        );
        if (!story.industries.some((i) => industryNames.includes(i.toLowerCase()))) {
          return false;
        }
      }

      // Filter by region
      if (filters.region.length > 0) {
        const regionNames = filters.region.map((id) =>
          getTagName("region", id).toLowerCase()
        );
        if (!story.regions.some((r) => regionNames.includes(r.toLowerCase()))) {
          return false;
        }
      }

      // Filter by technology
      if (filters.technology.length > 0) {
        const techNames = filters.technology.map((id) =>
          getTagName("technology", id).toLowerCase()
        );
        if (!story.technologies.some((t) => techNames.includes(t.toLowerCase()))) {
          return false;
        }
      }

      return true;
    });
  }, [stories, searchTerm, filters, tagGroups]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value.toLowerCase());
  };

  const handleFilterChange = (field: keyof FilterState, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value === "all" ? [] : [value],
    }));
  };

  const resetFilters = () => {
    setSearchTerm("");
    setFilters({
      industry: [],
      region: [],
      technology: [],
    });
  };

  const handleAction = async (action: string, id: string) => {
    const storyTitle = stories.find((story) => story.id === id)?.title;

    switch (action) {
      case "edit":
        break;
      case "archive":
        toast({
          title: "Story Archived",
          description: `"${storyTitle}" has been archived successfully.`,
        });
        break;
      case "delete":
        toast({
          title: "Story Deleted",
          description: `"${storyTitle}" has been deleted successfully.`,
        });
        break;
      case "duplicate":
        toast({
          title: "Story Duplicated",
          description: `A copy of "${storyTitle}" has been created.`,
        });
        break;
      default:
        break;
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="space-y-6">
      <RepositoryHeader />

      <div className="flex flex-col md:flex-row gap-4">
        <SearchBar searchTerm={searchTerm} onSearchChange={handleSearch} />
        <FilterSection
          filters={filters}
          onFilterChange={handleFilterChange}
          onResetFilters={resetFilters}
          searchTerm={searchTerm}
        />
      </div>

      <SuccessStoriesTable
        stories={filteredStories}
        onAction={handleAction}
        onResetFilters={resetFilters}
      />
      <RepositoryPagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
      />
    </div>
  );
};

export default Repository;
