import { supabase } from '../config/db.js';
import fs from 'fs';
import path from 'path';
import { logger } from './logger.js';

export const initializeDatabase = async () => {
  try {
    // Read the SQL file
    const sqlPath = path.join(process.cwd(), 'server', 'migrations', 'initial_schema.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    // Split the SQL content into individual statements
    const statements = sqlContent
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);

    // Execute each statement separately
    for (const statement of statements) {
      const { error } = await supabase
        .rpc('exec_sql', { sql: statement })
        .single();
      
      if (error) {
        logger.error('Migration error:', error);
        throw error;
      }
    }

    logger.info('Database initialization completed successfully');
  } catch (error) {
    logger.error('Database initialization failed:', error);
    throw error;
  }
};