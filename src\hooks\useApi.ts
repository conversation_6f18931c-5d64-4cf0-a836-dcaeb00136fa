import { useState, useMemo } from 'react';
import axios, { AxiosInstance, InternalAxiosRequestConfig } from 'axios';

// Create an Axios instance and export it
export const apiClient: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL, // Assuming your API calls are relative to this
});

// Add a request interceptor to include the token
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem('winwise_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export const useApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const post = async (url: string, data: any) => {
    setLoading(true);
    setError(null);
    try {
      // Use the apiClient instance which has the interceptor
      const response = await apiClient.post(url, data);
      return response.data;
    } catch (err) {
      if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.message || err.message);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  // Example GET method using the same instance
  const get = async (url: string, params?: any) => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiClient.get(url, { params });
      return response.data;
    } catch (err) {
      if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.message || err.message);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };


  return { post, get, loading, error }; // apiClient is now a named export
};