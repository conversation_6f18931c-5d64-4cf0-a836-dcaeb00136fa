import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

export class DashboardService {
  async getMetrics() {
    try {
      const [totalStories, activeUsers, totalIndustries] = await Promise.all([
        supabase
          .from('success_stories')
          .select('id', { count: 'exact' })
          .eq('flag_deleted', false),
        
        supabase
          .from('users')
          .select('id', { count: 'exact' })
          .eq('flag_active', true),
        
        supabase
          .from('tags')
          .select('id', { count: 'exact' })
          .eq('type', 'industry')
          .eq('flag_deleted', false)
      ]);

      return {
        totalStories: totalStories.count || 0,
        activeUsers: activeUsers.count || 0,
        totalIndustries: totalIndustries.count || 0
      };
    } catch (error) {
      throw error;
    }
  }

  async getRecentStories(limit = 5) {
    try {
      const { data: stories, error } = await supabase
        .from('success_stories')
        .select(`
          id,
          title,
          summary,
          created_at,
          industry:tags!industry_id(name),
          owner:users!owner_id(name)
        `)
        .eq('flag_deleted', false)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return stories;
    } catch (error) {
      throw error;
    }
  }

  async getIndustryDistribution() {
    try {
      const { data: industries, error: industriesError } = await supabase
        .from('tags')
        .select('id, name')
        .eq('type', 'industry')
        .eq('flag_deleted', false);

      if (industriesError) throw industriesError;

      const distribution = await Promise.all(
        industries.map(async (industry) => {
          const { count, error } = await supabase
            .from('success_stories')
            .select('id', { count: 'exact' })
            .eq('industry_id', industry.id)
            .eq('flag_deleted', false);

          if (error) throw error;

          return {
            industry: industry.name,
            count: count || 0
          };
        })
      );

      return distribution.sort((a, b) => b.count - a.count);
    } catch (error) {
      throw error;
    }
  }
}

export default new DashboardService();