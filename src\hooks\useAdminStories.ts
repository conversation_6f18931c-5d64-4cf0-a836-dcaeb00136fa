import { useState, useEffect } from 'react';

export interface Asset {
  id?: string;
  url: string;
  kind?: string;
  name: string;
  mimeType?: string;
  type?: string;
  previewUrl: string;
}

export interface Tag {
  id: string;
  name: string;
  type: string;
  status: boolean;
}

export interface Story {
  id: string;
  title: string;
  executive_summary: string;
  client_segment: string;
  oneline_summary: string;
  business_challenges: string;
  success_story_detail: string;
  industries: string[];
  regions: string[];
  technologies: string[];
  outcomes: string[];
  general_tags: string[];
  image_assets: Asset[];
  audio_assets: Asset[];
  video_assets: Asset[];
  document_assets: Asset[];
  demo_assets: Asset[];
  owner_id: string;
  status: 'draft' | 'published' | 'archived';
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  flag_deleted: boolean;
  owner: {
    name: string;
    id: string;  // Add this line
  };
  owner_name: string;
  has_pdf: boolean;
  has_video: boolean;
  has_audio: boolean;
  has_image: boolean;
}

export interface Filters {
  industries?: string[];
  regions?: string[];
  technologies?: string[];
  outcomes?: string[];
  tags?: string[];
  title?: string;
}

// Utilities
const transformIds = (ids: string[] | string | null, map: Map<string, string>): string[] => {
  if (!ids) return [];
  try {
    const parsed = typeof ids === 'string' ? JSON.parse(ids) : ids;
    return parsed.map((id: string) => map.get(id) || id);
  } catch {
    return [];
  }
};

const fetchTagNames = async (ids: string[]): Promise<Map<string, string>> => {
  if (!ids.length) return new Map();
  try {
    const token = localStorage.getItem('winwise_token');
    const queryParams = new URLSearchParams();
    ids.forEach(id => queryParams.append('ids', id));
    
    const res = await fetch(`${import.meta.env.VITE_API_URL}/api/tags/by-ids?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
    });
    if (!res.ok) {
      // Handle non-OK responses, e.g., by throwing an error or returning an empty map
      console.error('Error fetching tag names, status:', res.status);
      const errorData = await res.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to fetch tag names with status ${res.status}`);
    }
    const { data } = await res.json();
    return new Map(data.map((tag: Tag) => [tag.id, tag.name]));
  } catch (error) {
    console.error('Error fetching tag names:', error);
    return new Map();
  }
};

const fetchUserNames = async (userIds: string[]): Promise<Map<string, string>> => {
  const names = new Map<string, string>();
  await Promise.all(userIds.map(async id => {
    try {
      const res = await fetch(`${import.meta.env.VITE_API_URL}/api/auth/users/${id}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('winwise_token')}`
        }
      });
      if (!res.ok) {
        console.error(`Error fetching user name for ${id}, status:`, res.status);
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to fetch user name for ${id} with status ${res.status}`);
      }
      const { data } = await res.json();
      names.set(id, data?.name || '');
    } catch (error) {
      console.error(`Error fetching user name for ${id}:`, error);
      names.set(id, '');
    }
  }));
  return names;
};

// Hook
export const useAllStories = (filters?: Filters) => {
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    const fetchStories = async () => {
      try {
        const token = localStorage.getItem('winwise_token');
        const res = await fetch(`${import.meta.env.VITE_API_URL}/api/stories?&page=${currentPage}`, {
          headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` })
          }
        });
        if (!res.ok) {
          console.error('Error fetching stories, status:', res.status);
          const errorData = await res.json().catch(() => ({}));
          throw new Error(errorData.message || `Failed to fetch stories with status ${res.status}`);
        }
        const { data, totalPages: apiTotalPages } = await res.json();

        const rows = data.rows || [];

        const tagIdSet = new Set<string>();
        const ownerIdSet = new Set<string>();

        rows.forEach((story: any) => {
          ['industries', 'regions', 'technologies', 'outcomes', 'tags'].forEach(key => {
            const ids = story[key];
            const parsed = typeof ids === 'string' ? JSON.parse(ids) : ids;
            if (Array.isArray(parsed)) parsed.forEach((id: string) => tagIdSet.add(id));
          });
          if (story.owner_id) ownerIdSet.add(story.owner_id);
        });

        const [tagsMap, ownerNamesMap] = await Promise.all([
          fetchTagNames(Array.from(tagIdSet)),
          fetchUserNames(Array.from(ownerIdSet))
        ]);

        const transformed = rows.map((story: any): Story => {
          const transformField = (key: string) => transformIds(story[key], tagsMap);

          // In the transformed object creation
          return {
            id: story.id,
            title: story.title,
            executive_summary: story.executive_summary,
            client_segment: story.client_segment,
            oneline_summary: story.oneline_summary,
            business_challenges: story.business_challenges,
            success_story_detail: story.success_story_detail,
            industries: transformField('industries'),
            regions: transformField('regions'),
            technologies: transformField('technologies'),
            outcomes: transformField('outcomes'),
            general_tags: transformField('general_tags'),
            image_assets: story.image_assets || [],
            audio_assets: story.audio_assets || [],
            video_assets: story.video_assets || [],
            document_assets: story.document_assets || [],
            demo_assets: story.demo_assets || [],
            owner_id: story.owner_id,
            owner: {
              name: ownerNamesMap.get(story.owner_id) || '',
              id: story.owner_id  // Add this line
            },
            owner_name: ownerNamesMap.get(story.owner_id) || '',
            status: story.status,
            created_at: story.created_at,
            updated_at: story.updated_at,
            created_by: story.created_by,
            updated_by: story.updated_by,
            flag_deleted: !!story.flag_deleted,
            has_pdf: (story.document_assets?.length || 0) > 0,
            has_video: (story.video_assets?.length || 0) > 0,
            has_audio: (story.audio_assets?.length || 0) > 0,
            has_image: (story.image_assets?.length || 0) > 0
          };
        });

        setStories(transformed);
        setTotalPages(apiTotalPages);
      } catch (err) {
        console.error('Error fetching stories:', err);
        setError('Failed to fetch stories');
      } finally {
        setLoading(false);
      }
    };

    fetchStories();
  }, [filters, currentPage]);

  return {
    stories: stories.map(story => ({
      ...story,
      owner: {
        name: story.owner.name,
        id: story.owner_id
      }
    })),
    loading,
    error,
    totalPages,
    currentPage,
    setCurrentPage
  };
};
