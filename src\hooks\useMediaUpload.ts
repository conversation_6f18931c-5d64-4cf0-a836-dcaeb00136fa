import { useState } from "react";
import axios from "axios";

const N8N_WEBHOOK = "https://n8n.getondataconsulting.in/webhook";
const WEBHOOK_URLS = {
  CREATE: `${N8N_WEBHOOK}/2989b645-1f7b-4c73-9c66-2b1a05fcda49`,
  AUDIO: `${N8N_WEBHOOK}/f0e8c8ba-3f9d-4a01-a34b-ceb599a6e950`,
  IMAGE: `${N8N_WEBHOOK}/1b0acdf0-c76c-45d3-a893-12d2956500c5`,
  VIDEO: `${N8N_WEBHOOK}/3c4c8888-1971-48f8-bbb0-6607b8abb281`,
  DOCUMENT: `${N8N_WEBHOOK}/a9cae63a-1839-4fe8-9dd9-fd7b287f2487`,
};

export const useMediaUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createRequest = async (requestId: string) => {
    try {
     
      const response = await axios.post(WEBHOOK_URLS.CREATE, {
        requestid: requestId,
      });
      return response.data;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Create request failed";
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const uploadAudio = async (requestId: string, files: File[]) => {
    setIsUploading(true);
    setError(null);
    const formData = new FormData();
    formData.append("requestid", requestId);
    files.forEach((file) => {
      formData.append("audio", file);
    });

    try {
   
      const response = await axios.post(WEBHOOK_URLS.AUDIO, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      return response.data;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Audio upload failed";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const uploadImage = async (requestId: string, files: File[]) => {
    setIsUploading(true);
    setError(null);
    const formData = new FormData();
    formData.append("requestid", requestId);
    files.forEach((file) => {
      formData.append("image", file);
    });

    try {
    
      const response = await axios.post(WEBHOOK_URLS.IMAGE, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      return response.data;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Image upload failed";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const uploadVideo = async (requestId: string, files: File[]) => {
    setIsUploading(true);
    setError(null);
    const formData = new FormData();
    formData.append("requestid", requestId);
    files.forEach((file) => {
      formData.append("video", file);
    });

    try {
 
      const response = await axios.post(WEBHOOK_URLS.VIDEO, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      return response.data;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Video upload failed";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const uploadDocument = async (requestId: string, files: File[]) => {
    setIsUploading(true);
    setError(null);
    const formData = new FormData();
    formData.append("requestid", requestId);
    files.forEach((file) => {
      formData.append("document", file);
    });

    try {
 
      const response = await axios.post(WEBHOOK_URLS.DOCUMENT, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      return response.data;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Document upload failed";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  return {
    createRequest,
    uploadAudio,
    uploadImage,
    uploadVideo,
    uploadDocument,
    isUploading,
    error,
  };
};
