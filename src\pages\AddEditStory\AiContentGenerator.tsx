import { useState } from "react";
import {
  Loader2,
  FileText,
  Link as LinkIcon,
  MessageSquare,
  Upload,
  X,
  Wand2,
  LucideIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { toast } from "react-toastify";
import { useDocumentProcessing } from "@/hooks/useDocumentProcessing";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useStory } from "./StoryContext";

const TAB_OPTIONS = [
  { value: "pdf", label: "Upload Files", icon: FileText, mobileLabel: "PDF" },
  { value: "url", label: "From URL", icon: LinkIcon, mobileLabel: "URL" },
  { value: "text", label: "Text Description", icon: MessageSquare, mobileLabel: "TEXT" },
] as const;

type TabValue = typeof TAB_OPTIONS[number]["value"];

interface TabOption {
  value: TabValue;
  label: string;
  icon: LucideIcon;
  mobileLabel: string;
}

const VALID_FILE_TYPES = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];

export const AiContentGenerator = () => {
  const {
    aiState,
    files,
    setAiSource,
    setSourceUrl,
    handleAiGenerate: handleAiGenerateFromContext,
    handleFileUpload,
    resetFiles,
    aiDescription,
    setAiDescription,
    setDocumentResponse,
  } = useStory();

  const [isInputProvided, setIsInputProvided] = useState(false);
  const [isGenerated, setIsGenerated] = useState(false);
  const [activeTab, setActiveTab] = useState<TabValue>("pdf");

  const { processDocument, isProcessing } = useDocumentProcessing({
    onSuccess: (data) => {
      setDocumentResponse(data);
      handleAiGenerateFromContext();
      setIsGenerated(true);
      toast.success("Content generated successfully");
    },
    onError: (error) => {
      toast.error("Failed to generate content: " + error.message);
    },
  });

  const handleInputChange = {
    url: (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSourceUrl(value);
      setIsInputProvided(!!value);
    },
    text: (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const value = e.target.value;
      setAiDescription(value);
      setIsInputProvided(!!value);
    },
    file: (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        if (!VALID_FILE_TYPES.includes(file.type)) {
          toast.error(
            "Invalid file type. Please upload a PDF or document file."
          );
          return;
        }
        handleFileUpload("pdf", e); // Assuming "pdf" type for this handler
        setIsInputProvided(true);
      }
    },
  };

const clearInputs: Record<TabValue, () => void> = {
  pdf: () => {
    resetFiles("pdf");
    setIsInputProvided(false);
    setIsGenerated(false);
  },
  url: () => {
    setSourceUrl("");
    setIsInputProvided(false);
    setIsGenerated(false);
  },
  text: () => {
    setAiDescription("");
    setIsInputProvided(false);
    setIsGenerated(false);
  },
};

const handleTabChange = (value: TabValue) => {
  if (value !== activeTab) {
    setAiSource(value);
    setActiveTab(value);
    // Clear other inputs
    (Object.keys(clearInputs) as TabValue[])
      .filter((key) => key !== value)
      .forEach((keyToClear) => clearInputs[keyToClear]());
    setIsInputProvided(false); // Reset for the new tab
  }
};

const handleAiGenerate = async () => {
    try {
     
      await processDocument(
        aiState.source === "pdf" ? files.pdf : null,
        aiState.source === "url" ? aiState.sourceUrl : undefined,
        aiState.source === "text" ? aiDescription : undefined
      );
      
    } catch (error) {
      console.error("Document processing failed:", error);
    }
  };

  const renderTabContent = {
    pdf: () => (
      <div className="border-2 border-dashed rounded-md p-6 text-center">
        {files.pdf ? (
          <div className="flex items-center justify-between bg-secondary/50 p-2 rounded">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <span className="text-sm font-medium">{files.pdf.name}</span>
              <span className="text-xs text-muted-foreground">
                ({Math.round(files.pdf.size / 1024)} KB)
              </span>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={clearInputs.pdf}
              className="h-7 w-7"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <>
            <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
            <p className="text-sm font-medium mb-2">
              Transform your documents into engaging stories - Upload PDF or Word
            </p>
            <p className="text-xs text-muted-foreground mb-4">
              Upload an existing case study, white paper, or product document
            </p>
            <Button
              variant="outline"
              onClick={() => document.getElementById("pdfInput")?.click()}
              className="mx-auto"
            >
              Select File
            </Button>
            <input
              id="pdfInput"
              type="file"
              accept=".pdf,.doc,.docx"
              className="hidden"
              onChange={(e) => handleInputChange.file(e)}
            />
          </>
        )}
      </div>
    ),
    url: () => (
      <div className="space-y-2">
        <Input
          placeholder="Enter URL to existing content (e.g., website, article)"
          value={aiState.sourceUrl}
          onChange={handleInputChange.url}
          disabled={activeTab !== "url"}
        />
        <p className="text-xs text-muted-foreground">
          {activeTab !== "url"
            ? "Please switch to URL tab to use URL input"
            : "We'll extract and analyze the content from the provided URL"}
        </p>
      </div>
    ),
    text: () => (
      <div className="space-y-2">
        <Textarea
          placeholder="Describe your success story in detail. Include information about the customer, challenge, solution, and results..."
          className="min-h-[150px] resize-none"
          value={aiDescription}
          onChange={handleInputChange.text}
          disabled={activeTab !== "text"}
        />
        <p className="text-xs text-muted-foreground">
          {activeTab !== "text"
            ? "Please switch to Text tab to use text input"
            : "Our AI will generate a structured story based on your description"}
        </p>
      </div>
    ),
  };

  const isGenerateDisabled =
    isProcessing ||
    isGenerated ||
    (aiState.source === "pdf" && !files.pdf) ||
    (aiState.source === "url" && !aiState.sourceUrl) ||
    (aiState.source === "text" && !aiDescription);

  return (
    <Card className="bg-gradient-to-br from-mobio-lavender/20 to-mobio-lightblue/20">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center">
          <Wand2 className="h-5 w-5 mr-2 text-mobio-blue" />
          Smart Content Generation
        </CardTitle>
        <CardDescription>
          Upload existing content or provide a URL to auto-generate your story
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        <Tabs
          defaultValue="pdf"
          onValueChange={(value) => handleTabChange(value as TabValue)}
        >
          <TabsList className="w-full grid grid-cols-3 md:flex">
            {TAB_OPTIONS.map((tabOption: TabOption) => (
              <TabsTrigger
                key={tabOption.value}
                value={tabOption.value}
                className="flex-1"
                disabled={activeTab !== tabOption.value && isInputProvided}
              >
                <tabOption.icon className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">{tabOption.label}</span>
                <span className="sm:hidden">{tabOption.mobileLabel}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {TAB_OPTIONS.map((tabOption: TabOption) => (
            <TabsContent
              key={tabOption.value}
              value={tabOption.value}
              className="mt-4 space-y-4"
            >
              {renderTabContent[tabOption.value]()}
            </TabsContent>
          ))}

          <div className="mt-4 flex justify-end">
            <Button
              onClick={handleAiGenerate}
              disabled={isGenerateDisabled}
              className="bg-mobio-blue text-white hover:bg-mobio-blue/90"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : isGenerated ? (
                "Content Generated"
              ) : (
                <>
                  <Wand2 className="mr-2 h-4 w-4" />
                  Generate Content
                </>
              )}
            </Button>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
};
