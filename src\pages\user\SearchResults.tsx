
import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FileText, Video, Headphones, Image as ImageIcon, Filter } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Sheet,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

// Mock Data (same as in Home.tsx)
const mockStories = [
  {
    id: "1",
    title: "AI-Powered Inventory Management Transformation",
    summary: "Global retailer improved efficiency by 40% using our AI inventory solution",
    industry: "Retail",
    region: "APAC",
    technology: "AI",
    hasVideo: true,
    hasAudio: false,
    hasPdf: true,
    hasImage: true,
  },
  {
    id: "2",
    title: "Healthcare Patient Experience Revolution",
    summary: "Major hospital chain reduced wait times by 60% with our patient management platform",
    industry: "Healthcare",
    region: "US",
    technology: "Data Analytics",
    hasVideo: false,
    hasAudio: true,
    hasPdf: true,
    hasImage: true,
  },
  {
    id: "3",
    title: "Financial Services Fraud Detection Enhancement",
    summary: "Leading bank reduced fraud by 87% using our machine learning solution",
    industry: "FinTech",
    region: "Europe",
    technology: "Machine Learning",
    hasVideo: true,
    hasAudio: true,
    hasPdf: true,
    hasImage: false,
  },
  {
    id: "4",
    title: "Supply Chain Optimization Solution",
    summary: "E-commerce leader reduced shipping costs by 32% with our logistics platform",
    industry: "Logistics",
    region: "Global",
    technology: "IoT",
    hasVideo: false,
    hasAudio: false,
    hasPdf: true,
    hasImage: true,
  },
];

const StoryCard = ({ story }: { story: typeof mockStories[0] }) => (
  <Card className="w-full hover:shadow-md transition-shadow">
    <CardContent className="p-4">
      <h3 className="font-medium text-base mb-1">
        <Link to={`/story/${story.id}`} className="hover:text-primary">
          {story.title}
        </Link>
      </h3>
      <p className="text-muted-foreground text-sm mb-3 line-clamp-2">{story.summary}</p>
      <div className="flex flex-wrap gap-2 mb-3">
        <Badge variant="outline" className="bg-mobio-lavender">{story.industry}</Badge>
        <Badge variant="outline" className="bg-mobio-lightblue">{story.region}</Badge>
        <Badge variant="outline" className="bg-mobio-gray">{story.technology}</Badge>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-muted-foreground">
          {story.hasPdf && <FileText className="h-4 w-4" />}
          {story.hasVideo && <Video className="h-4 w-4" />}
          {story.hasAudio && <Headphones className="h-4 w-4" />}
          {story.hasImage && <ImageIcon className="h-4 w-4" />}
        </div>
        <Button variant="outline" size="sm" asChild>
          <Link to={`/story/${story.id}`}>View Story</Link>
        </Button>
      </div>
    </CardContent>
  </Card>
);

const FilterPane = ({ onApplyFilters }: { onApplyFilters: () => void }) => {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h4 className="text-sm font-medium">Industry</h4>
        <div className="space-y-1.5">
          {["Retail", "Healthcare", "FinTech", "Logistics", "Manufacturing"].map((industry) => (
            <div key={industry} className="flex items-center gap-2">
              <input type="checkbox" id={`industry-${industry}`} className="h-4 w-4" />
              <label htmlFor={`industry-${industry}`} className="text-sm">{industry}</label>
            </div>
          ))}
        </div>
      </div>
      
      <Separator />
      
      <div className="space-y-2">
        <h4 className="text-sm font-medium">Technology</h4>
        <div className="space-y-1.5">
          {["AI", "Machine Learning", "Data Analytics", "IoT", "Blockchain"].map((tech) => (
            <div key={tech} className="flex items-center gap-2">
              <input type="checkbox" id={`tech-${tech}`} className="h-4 w-4" />
              <label htmlFor={`tech-${tech}`} className="text-sm">{tech}</label>
            </div>
          ))}
        </div>
      </div>
      
      <Separator />
      
      <div className="space-y-2">
        <h4 className="text-sm font-medium">Region</h4>
        <div className="space-y-1.5">
          {["US", "Europe", "APAC", "Global"].map((region) => (
            <div key={region} className="flex items-center gap-2">
              <input type="checkbox" id={`region-${region}`} className="h-4 w-4" />
              <label htmlFor={`region-${region}`} className="text-sm">{region}</label>
            </div>
          ))}
        </div>
      </div>
      
      <div className="pt-2">
        <Button onClick={onApplyFilters} className="w-full">Apply Filters</Button>
      </div>
    </div>
  );
};

const SearchResults = () => {
  const [loading, setLoading] = useState(true);
  const [results, setResults] = useState<typeof mockStories>([]);
  const [sortBy, setSortBy] = useState("relevant");
  const location = useLocation();
  const searchQuery = new URLSearchParams(location.search).get('q') || '';

  useEffect(() => {
    // Simulate API call
    setLoading(true);
    setTimeout(() => {
      // Filter stories based on query
      setResults(mockStories);
      setLoading(false);
    }, 1000);
  }, [searchQuery]);

  const handleApplyFilters = () => {
    // In real app, this would update results based on selected filters
    console.log("Filters applied");
  };  

  const handleSort = (value: string) => {
    setSortBy(value);
    // In real app, this would sort results
  };

  return (
    <div className="container max-w-5xl mx-auto space-y-6 pb-16 md:pb-4">
      <section className="space-y-2">
        <h1 className="text-2xl font-bold">Search Results</h1>
        <p className="text-muted-foreground">
          {searchQuery ? `Results for: "${searchQuery}"` : "All success stories"}
        </p>
      </section>

      <div className="flex justify-between items-center py-2">
        <div className="flex items-center gap-2">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Filter className="h-4 w-4" /> Filters
              </Button>
            </SheetTrigger>
            <SheetContent side="left">
              <SheetHeader>
                <SheetTitle>Filter Results</SheetTitle>
              </SheetHeader>
              <div className="py-4">
                <FilterPane onApplyFilters={handleApplyFilters} />
              </div>
            </SheetContent>
          </Sheet>
          <p className="text-sm text-muted-foreground">
            {results.length} results
          </p>
        </div>

        <Select value={sortBy} onValueChange={handleSort}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="relevant">Most Relevant</SelectItem>
            <SelectItem value="recent">Most Recent</SelectItem>
            <SelectItem value="viewed">Most Viewed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {loading ? (
          // Loading skeletons
          Array(4).fill(null).map((_, i) => (
            <Card key={i} className="w-full">
              <CardContent className="p-4">
                <Skeleton className="h-5 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-full mb-3" />
                <div className="flex gap-2 mb-3">
                  <Skeleton className="h-6 w-16 rounded-full" />
                  <Skeleton className="h-6 w-16 rounded-full" />
                  <Skeleton className="h-6 w-16 rounded-full" />
                </div>
                <div className="flex justify-between">
                  <div className="flex gap-2">
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 w-4 rounded-full" />
                  </div>
                  <Skeleton className="h-8 w-20 rounded-md" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : results.length > 0 ? (
          results.map((story) => (
            <StoryCard key={story.id} story={story} />
          ))
        ) : (
          <p className="col-span-2 text-center py-8 text-muted-foreground">
            No stories found matching your search criteria.
          </p>
        )}
      </div>
      
      <div className="flex justify-center pt-6">
        <Button variant="outline" disabled={loading}>
          Load More
        </Button>
      </div>
    </div>
  );
};

export default SearchResults;
