
import { Lightbulb, Sparkles, Plus, AlertCircle, CheckCircle, Tag, FileText } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useStory } from "./StoryContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export const AiAssistance = () => {
  const { 
    suggestedTags, 
    handleAddSuggestedTag, 
    handleAiGenerate, 
    aiState, 
    files 
  } = useStory();
  
  return (
    <Card className="bg-mobio-lavender/30">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center">
          <Sparkles className="h-5 w-5 mr-2" /> AI Assistance
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-medium flex items-center mb-2">
            <Lightbulb className="h-4 w-4 mr-2 text-amber-500" /> AI Tools
          </h4>
          <Button 
            variant="outline" 
            onClick={handleAiGenerate}
            className="w-full mb-2 justify-start text-left"
            disabled={aiState.isProcessing || (!files.pdf && aiState.source === "pdf") || (aiState.source === "url" && !aiState.sourceUrl)}
          >
            <FileText className="h-4 w-4 mr-2" />
            <span>Generate content from source</span>
          </Button>
          <Button 
            variant="outline" 
            className="w-full mb-2 justify-start text-left"
          >
            <Tag className="h-4 w-4 mr-2" />
            <span>Auto-suggest tags</span>
          </Button>
        </div>
        
        <div>
          <h4 className="font-medium flex items-center mb-2">
            <Tag className="h-4 w-4 mr-2 text-mobio-blue" /> Suggested Tags
          </h4>
          {suggestedTags.map((tag) => (
            <div key={tag.id} className="flex items-center justify-between mb-2 p-2 bg-white rounded-md">
              <div>
                <p className="font-medium text-sm">{tag.name}</p>
                <p className="text-xs text-muted-foreground">{tag.category}</p>
              </div>
              <Button size="sm" variant="ghost" onClick={() => handleAddSuggestedTag(tag.id)}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
        
        <div>
          <h4 className="font-medium flex items-center mb-2">
            <AlertCircle className="h-4 w-4 mr-2 text-amber-500" /> Missing Information
          </h4>
          <div className="space-y-1.5 text-sm">
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
              <p>Title is complete</p>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
              <p>Industry is selected</p>
            </div>
            <div className="flex items-start gap-2">
              <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
              <p>Consider adding at least one attachment</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const RoleVisibility = () => {
  const { storyData, handleRoleVisibilityChange } = useStory();
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Role-based Visibility</CardTitle>
        <CardDescription>
          Control who can see this success story
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {[
            { id: "cxo", label: "CXO / Executive" },
            { id: "sales", label: "Sales Team" },
            { id: "presales", label: "Presales Team" },
            { id: "marketing", label: "Marketing Team" },
            { id: "delivery", label: "Delivery Team" }
          ].map((role) => (
            <div key={role.id} className="flex items-center space-x-2">
              <Checkbox 
                id={`role-${role.id}`}
                checked={storyData.roleVisibility[role.id as keyof typeof storyData.roleVisibility]}
                onCheckedChange={(checked) => 
                  handleRoleVisibilityChange(role.id, !!checked)
                }
              />
              <Label 
                htmlFor={`role-${role.id}`}
                className="text-sm font-normal"
              >
                {role.label}
              </Label>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export const VersionHistory = () => {
  const { isEditMode } = useStory();
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Version History</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 text-sm">
          {isEditMode ? (
            <>
              <div className="flex justify-between items-center">
                <p>Current Version</p>
                <Badge variant="outline">v2.0</Badge>
              </div>
              <div className="border-l-2 border-muted pl-3 py-1">
                <p className="font-medium">Updated by Sarah Johnson</p>
                <p className="text-xs text-muted-foreground">May 12, 2025 at 2:34 PM</p>
              </div>
              <div className="border-l-2 border-muted pl-3 py-1">
                <p className="font-medium">Initial Version</p>
                <p className="text-xs text-muted-foreground">May 5, 2025 at 10:15 AM</p>
              </div>
            </>
          ) : (
            <p className="text-muted-foreground">
              Version history will be available after saving.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export const SidebarComponents = () => (
  <div className="space-y-6">
    <AiAssistance />
    <RoleVisibility />
    <VersionHistory />
  </div>
);
