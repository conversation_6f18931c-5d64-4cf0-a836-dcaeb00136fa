
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Search } from "lucide-react";
import StoryRow from "./StoryRow";
import { SuccessStory } from "./types";
interface SuccessStoriesTableProps {
  stories: SuccessStory[];
  onAction: (action: string, id: string) => void;
  onResetFilters: () => void;
}

const SuccessStoriesTable = ({ stories, onAction, onResetFilters }: SuccessStoriesTableProps) => {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[40%]">Title</TableHead>
            <TableHead>Industry</TableHead>
            <TableHead className="hidden md:table-cell">Region</TableHead>
            <TableHead className="hidden md:table-cell">Status</TableHead>
            <TableHead className="hidden lg:table-cell">Owner</TableHead>
            <TableHead className="hidden md:table-cell">Files</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {stories.length > 0 ? (
            stories.map((story) => (
              <StoryRow key={story.id} story={story} onAction={onAction} />
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-10 text-muted-foreground">
                <div className="flex flex-col items-center space-y-2">
                  <Search className="h-8 w-8 opacity-40" />
                  <p>No success stories found matching your search criteria.</p>
                  <Button variant="link" onClick={onResetFilters}>
                    Clear all filters
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default SuccessStoriesTable;
