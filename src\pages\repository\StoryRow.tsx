import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { TableCell, TableRow } from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash, MoreHorizontal } from "lucide-react";
import StoryAttachmentIcons from "./StoryAttachmentIcons";
import { SuccessStory } from "./types";
import { useTagNames } from "@/hooks/useTagNames";

interface StoryRowProps {
  story: SuccessStory;
  onAction: (action: string, id: string) => void;
}

const StoryRow = ({ story, onAction }: StoryRowProps) => {
  const { getTagNames } = useTagNames(); // Add this hook
  
  const hasAttachments = {
    image: story.image_assets?.length > 0,
    audio: story.audio_assets?.length > 0,
    video: story.video_assets?.length > 0,
    document: story.document_assets?.length > 0,
    demo: story.demo_assets?.length > 0,
  };

  const getStatusVariant = (status: string) => {
    if (status === "published") return "default";
    if (status === "archived") return "secondary";
    return "outline";
  };
  
  return (
    <TableRow key={story.id}>
      <TableCell className="font-medium">
        <div>
          <Link to={`/story/${story.id}`} className="block line-clamp-2">
            {story.title}
          </Link>
          <div className="md:hidden text-xs text-muted-foreground mt-1">
            {(story.regions ?? []).join(", ")} • {story.status}
          </div>
        </div>
      </TableCell>
      <TableCell>
        {getTagNames(story.industries).map((industry) => (
          <Badge key={industry} variant="outline" className="mr-1">
            {industry}
          </Badge>
        ))}
      </TableCell>
      <TableCell className="hidden md:table-cell">
        {getTagNames(story.regions).join(", ")}
      </TableCell>
      <TableCell className="hidden md:table-cell">
        <Badge variant={getStatusVariant(story.status)}>
          {story.status}
        </Badge>
      </TableCell>
      <TableCell className="hidden lg:table-cell">{story.owner?.name}</TableCell>
      <TableCell className="hidden md:table-cell">
        <StoryAttachmentIcons attachments={hasAttachments} />
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem asChild>
              <Link to={`/admin/edit-story/${story.id}`}>
                <Edit className="h-4 w-4 mr-2" /> Edit
              </Link>
            </DropdownMenuItem>
            <Dialog>
              <DialogTrigger asChild>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                  <Trash className="h-4 w-4 mr-2" /> Delete
                </DropdownMenuItem>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Delete Success Story</DialogTitle>
                </DialogHeader>
                <div className="py-4">
                  <p>
                    Are you sure you want to delete this success story? This
                    action cannot be undone.
                  </p>
                </div>
                <div className="flex justify-end gap-3">
                  <Button variant="outline">Cancel</Button>
                  <Button
                    variant="destructive"
                    onClick={() => onAction("delete", story.id)}
                  >
                    Delete
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
};

export default StoryRow;