
import { Outlet } from "react-router-dom";
import UserNavbar from "./UserNavbar";
import UserFooter from "./UserFooter";
import { Suspense } from "react";
import { Loader2 } from "lucide-react";

const UserLayout = () => {
  return (
    <div className="min-h-screen flex flex-col bg-[#f8fafc]">
      <UserNavbar />
      <main className="flex-1 px-3 py-4 sm:px-4 sm:py-5 md:px-6 md:py-6 lg:px-8 lg:py-8 pb-16 md:pb-6">
        <Suspense fallback={
          <div className="flex h-[70vh] items-center justify-center">
            <div className="flex flex-col items-center gap-3">
              <Loader2 className="h-8 w-8 animate-spin text-mobio-blue" />
              <p className="text-sm text-muted-foreground animate-pulse-soft">Loading experience...</p>
            </div>
          </div>
        }>
          <div className="animate-fade-in md:animate-scale-in">
            <Outlet />
          </div>
        </Suspense>
      </main>
      <UserFooter />
    </div>
  );
};

export default UserLayout;
