interface ErrorDisplayProps {
  title?: string;
  message: string;
}

const ErrorDisplay = ({
  title = "Error Loading Data",
  message,
}: ErrorDisplayProps) => {
  return (
    <div className="flex h-[70vh] items-center justify-center">
      <div className="text-center">
        <h2 className="text-xl font-semibold text-red-600 mb-2">{title}</h2>
        <p className="text-muted-foreground">{message}</p>
      </div>
    </div>
  );
};

export default ErrorDisplay;