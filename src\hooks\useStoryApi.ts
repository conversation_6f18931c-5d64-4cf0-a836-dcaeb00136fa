import { useState } from 'react';
import axios from 'axios'; // Keep for AxiosError type checking if needed, or remove if not used directly
import { apiClient } from './useApi'; // Import the configured apiClient

export const useStoryApi = () => {
  const [isLoading, setIsLoading] = useState(false);

  const createStory = async (storyData: any) => {
    try {
      setIsLoading(true);
      
      // Log the curl equivalent of the request
      const curlCommand = `curl -X POST "${import.meta.env.VITE_API_URL}/api/stories" \
        -H "Content-Type: application/json" \
        -d '${JSON.stringify({
          ...storyData,
          status: 'draft'
        })}'`;
    
      // Use the apiClient instance
      const response = await apiClient.post(`/api/stories`, { // URL is relative to baseURL in apiClient
        ...storyData,
        status: 'draft'
      });
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        console.error('API Error:', error.response.data?.message || error.message);
      } else if (error instanceof Error) {
        console.error('API Error:', error.message);
      } else {
        console.error('API Error: An unknown error occurred');
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateStory = async (id: string, storyData: any) => {
    try {
      setIsLoading(true);
      

      // Use the apiClient instance
      const response = await apiClient.put(`/api/stories/${id}`, storyData); // URL is relative
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        console.error('API Error:', error.response.data?.message || error.message);
      } else if (error instanceof Error) {
        console.error('API Error:', error.message);
      } else {
        console.error('API Error: An unknown error occurred');
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  
  return {
    createStory,
    updateStory,
    isLoading
  };
};