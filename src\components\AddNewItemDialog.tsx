import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Slide,
} from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";
import { styled } from "@mui/material/styles";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const FixedWidthDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialog-paper": {
    width: "400px",
    maxWidth: "90%",
  },
}));

interface TagDialogProps {
  open: boolean;
  onClose: () => void;
  itemName: string;
  setItemName: (name: string) => void;
  itemType: string;
  setItemType: (type: string) => void;
  allCategories: string[];
  handleSave: () => void;
  isEditing: boolean;
}

const TagDialog: React.FC<TagDialogProps> = ({
  open,
  onClose,
  itemName,
  setItemName,
  itemType,
  setItemType,
  allCategories,
  handleSave,
  isEditing,
}) => {
  return (
    <FixedWidthDialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      onClose={onClose}
      aria-describedby="alert-dialog-slide-description"
    >
      <DialogTitle>{isEditing ? "Edit Tag" : "Add New Tag"}</DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-slide-description">
          Please enter the details for the tag.
        </DialogContentText>
        <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
          <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Tag Name"
            type="text"
            fullWidth
            variant="outlined"
            value={itemName}
            onChange={(e) => setItemName(e.target.value)}
          />
          <FormControl fullWidth variant="outlined" margin="dense">
            <InputLabel id="type-label">Tag Type</InputLabel>
            <Select
              labelId="type-label"
              id="type"
              value={itemType}
              onChange={(e) => setItemType(e.target.value)}
              label="Tag Type"
            >
              {allCategories.map((cat) => (
                <MenuItem
                  key={cat}
                  value={cat.toLowerCase().replace(/ /g, "_")}
                >
                  {cat}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </div>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button onClick={handleSave} color="primary">
          Save
        </Button>
      </DialogActions>
    </FixedWidthDialog>
  );
};

export default TagDialog;
