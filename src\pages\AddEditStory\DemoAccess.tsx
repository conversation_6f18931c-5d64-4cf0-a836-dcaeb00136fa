
import { ExternalLink, Key } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useStory } from "./StoryContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export const DemoAccess = () => {
  const { storyData, handleInputChange } = useStory();

  return (
    <Card className="border-mobio-lavender/40">
      <CardHeader className="bg-mobio-lavender/10">
        <CardTitle className="text-mobio-blue">Demo Access</CardTitle>
        <CardDescription>
          Provide access details for system demos and reviews
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 pt-5">
        <div>
          <Label htmlFor="demo-link" className="text-base font-medium">System Demo URL</Label>
          <div className="relative mt-1.5">
            <ExternalLink className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="demo-link"
              type="url"
              placeholder="https://example.com/demo"
              value={storyData.demoLink}
              onChange={(e) => handleInputChange("demoLink", e.target.value)}
              className="pl-9"
            />
          </div>
          <p className="text-xs text-muted-foreground mt-1">Link to access a working demo of this success story</p>
        </div>
        
        <div>
          <Label htmlFor="demo-access" className="text-base font-medium">Access Credentials</Label>
          <div className="relative mt-1.5">
            <Key className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Textarea
              id="demo-access"
              placeholder="Username: demo_user&#10;Password: Demo123!&#10;Additional instructions..."
              value={storyData.demoAccessDetails}
              onChange={(e) => handleInputChange("demoAccessDetails", e.target.value)}
              className="min-h-[100px] pl-9"
            />
          </div>
        </div>
        
        <div className="flex items-center space-x-2 pt-2">
          <Checkbox 
            id="show-to-users" 
            checked={storyData.showDemoToEndUsers}
            onCheckedChange={(checked) => handleInputChange("showDemoToEndUsers", !!checked)}
          />
          <Label 
            htmlFor="show-to-users" 
            className="text-sm font-normal"
          >
            Make demo access visible to end users
          </Label>
        </div>
      </CardContent>
    </Card>
  );
};
