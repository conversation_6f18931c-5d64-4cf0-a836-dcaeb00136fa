import express from "express";
// Route imports
import authRoutes from "./auth.routes.js";
import tagRoutes from "./tag.routes.js";
import documentRoutes from "./document.routes.js";
import storyRoutes from "./story.routes.js";

const router = express.Router();

router.use("/auth", authRoutes);
router.use("/tags", tagRoutes);
router.use("/documents", documentRoutes);
router.use("/stories", storyRoutes);

export default router;
