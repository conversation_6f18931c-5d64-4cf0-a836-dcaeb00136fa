import { logger } from '../utils/logger.js';

export const auditLogger = (req, res, next) => {
  // Log the request details
  logger.info({
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('user-agent'),
    timestamp: new Date().toISOString()
  });

  // Log response using response finish event
  res.on('finish', () => {
    logger.info({
      method: req.method,
      url: req.url,
      status: res.statusCode,
      timestamp: new Date().toISOString()
    });
  });

  next();
};