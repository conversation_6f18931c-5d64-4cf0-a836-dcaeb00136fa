
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  AlertCircle,
  Search,
  TagIcon,
  TrendingUp,
  FileCheck,
  Users,
  ArrowUpRight,
  ExternalLink,
  ArrowRight,
  CheckCircle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/components/ui/use-toast";
import { Link } from "react-router-dom";

const Intelligence = () => {
  const [activeTab, setActiveTab] = useState("content");

  const handleApplyFix = (issueType: string) => {
    toast({
      title: "Auto-fix Applied",
      description: `The system has automatically addressed the ${issueType} issue.`,
    });
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold">Admin Intelligence</h1>
      <p className="text-muted-foreground">
        Get insights and recommendations to improve your success stories
      </p>

      <Tabs defaultValue="content" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="content">
            <FileCheck className="h-4 w-4 mr-2" />
            Content Health
          </TabsTrigger>
          <TabsTrigger value="usage">
            <Users className="h-4 w-4 mr-2" />
            Usage Insights
          </TabsTrigger>
          <TabsTrigger value="optimization">
            <TrendingUp className="h-4 w-4 mr-2" />
            Optimization
          </TabsTrigger>
        </TabsList>

        <TabsContent value="content" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-3">
                    <FileCheck className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-xl">28</h3>
                  <p className="text-muted-foreground text-sm">Complete Stories</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center mb-3">
                    <AlertCircle className="h-6 w-6 text-amber-600" />
                  </div>
                  <h3 className="font-semibold text-xl">5</h3>
                  <p className="text-muted-foreground text-sm">Need Attention</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-3">
                    <TagIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-xl">97%</h3>
                  <p className="text-muted-foreground text-sm">Tag Coverage</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-3">
                    <Search className="h-6 w-6 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-xl">94%</h3>
                  <p className="text-muted-foreground text-sm">Search Match Rate</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <h2 className="text-lg font-medium mt-8 mb-4">Content Issues</h2>

          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-base">Missing Industry Tags</CardTitle>
                  <Badge variant="outline" className="bg-amber-50 text-amber-600 border-amber-200">
                    3 Stories
                  </Badge>
                </div>
                <CardDescription>
                  These stories are missing industry classification
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="space-y-2 text-sm">
                  <li className="p-2 bg-secondary rounded-md flex justify-between items-center">
                    <Link to="/edit-story/5" className="hover:underline">
                      Digital Transformation Case Study
                    </Link>
                    <Button size="sm" variant="outline" onClick={() => handleApplyFix("industry tag")}>
                      AI Fix
                    </Button>
                  </li>
                  <li className="p-2 bg-secondary rounded-md flex justify-between items-center">
                    <Link to="/edit-story/8" className="hover:underline">
                      Customer Experience Platform
                    </Link>
                    <Button size="sm" variant="outline" onClick={() => handleApplyFix("industry tag")}>
                      AI Fix
                    </Button>
                  </li>
                  <li className="p-2 bg-secondary rounded-md flex justify-between items-center">
                    <Link to="/edit-story/12" className="hover:underline">
                      Data Migration Success Story
                    </Link>
                    <Button size="sm" variant="outline" onClick={() => handleApplyFix("industry tag")}>
                      AI Fix
                    </Button>
                  </li>
                </ul>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="ghost" size="sm" className="ml-auto" asChild>
                  <Link to="/master-data">
                    Manage Industry Tags
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-base">Missing Attachments</CardTitle>
                  <Badge variant="outline" className="bg-amber-50 text-amber-600 border-amber-200">
                    2 Stories
                  </Badge>
                </div>
                <CardDescription>
                  These stories would benefit from supporting documents
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="space-y-2 text-sm">
                  <li className="p-2 bg-secondary rounded-md flex justify-between items-center">
                    <Link to="/edit-story/15" className="hover:underline">
                      Healthcare AI Implementation
                    </Link>
                    <Button size="sm" variant="outline" asChild>
                      <Link to="/edit-story/15">
                        Add PDF
                      </Link>
                    </Button>
                  </li>
                  <li className="p-2 bg-secondary rounded-md flex justify-between items-center">
                    <Link to="/edit-story/19" className="hover:underline">
                      Supply Chain Optimization
                    </Link>
                    <Button size="sm" variant="outline" asChild>
                      <Link to="/edit-story/19">
                        Add PDF
                      </Link>
                    </Button>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="usage" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Search Analytics</CardTitle>
              <CardDescription>
                Popular search terms and patterns from users
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-sm font-medium mb-3">Top Search Terms</h3>
                  <div className="space-y-2">
                    {[
                      { term: "Retail AI", count: 42 },
                      { term: "Healthcare analytics", count: 37 },
                      { term: "Supply chain optimization", count: 31 },
                      { term: "FinTech case study", count: 24 },
                      { term: "Cloud migration", count: 19 }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center">
                        <div className="flex-1">
                          <div className="flex justify-between mb-1">
                            <span className="text-sm font-medium">{item.term}</span>
                            <span className="text-sm text-muted-foreground">{item.count} searches</span>
                          </div>
                          <Progress value={(item.count / 42) * 100} className="h-1.5" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-3">Top Users</h3>
                    <div className="space-y-3">
                      {[
                        { user: "Michael Smith", role: "Sales Director", searches: 87 },
                        { user: "Lisa Wong", role: "Presales Consultant", searches: 76 },
                        { user: "James Brown", role: "Account Manager", searches: 65 }
                      ].map((user, index) => (
                        <div key={index} className="flex items-center gap-3 p-2 bg-secondary rounded-md">
                          <div className="w-8 h-8 rounded-full bg-mobio-blue/10 flex items-center justify-center">
                            <span className="text-xs font-medium text-mobio-blue">
                              {user.user.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">{user.user}</p>
                            <p className="text-xs text-muted-foreground">{user.role}</p>
                          </div>
                          <div className="text-sm text-muted-foreground">{user.searches}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-3">Content Gaps</h3>
                    <div className="space-y-3">
                      {[
                        { term: "Retail + APAC", frequency: "High", coverage: "Low" },
                        { term: "Healthcare + Security", frequency: "Medium", coverage: "Low" },
                        { term: "Manufacturing + AI", frequency: "High", coverage: "Medium" }
                      ].map((gap, index) => (
                        <div key={index} className="p-3 border rounded-md">
                          <div className="flex justify-between">
                            <p className="text-sm font-medium">{gap.term}</p>
                            <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">
                              Gap
                            </Badge>
                          </div>
                          <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                            <span>Search frequency: {gap.frequency}</span>
                            <span>Content coverage: {gap.coverage}</span>
                          </div>
                          <Button variant="link" size="sm" className="p-0 h-auto mt-1" asChild>
                            <Link to="/add-story">
                              Add New Story
                              <ArrowUpRight className="ml-1 h-3 w-3" />
                            </Link>
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Most Viewed Success Stories</CardTitle>
              <CardDescription>
                These stories have the highest engagement
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    title: "AI-Powered Inventory Optimization for Major US Retailer",
                    industry: "Retail",
                    views: 324,
                    downloads: 87,
                    shares: 42
                  },
                  {
                    title: "Healthcare Provider Improves Patient Outcomes with ML",
                    industry: "Healthcare",
                    views: 276,
                    downloads: 64,
                    shares: 38
                  },
                  {
                    title: "Global Bank Enhances Fraud Detection with Real-time AI",
                    industry: "Finance",
                    views: 215,
                    downloads: 59,
                    shares: 27
                  },
                  {
                    title: "Manufacturing Giant Reduces Downtime with Predictive Maintenance",
                    industry: "Manufacturing",
                    views: 187,
                    downloads: 43,
                    shares: 19
                  }
                ].map((story, index) => (
                  <div key={index} className="p-4 border rounded-md">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                      <div>
                        <h3 className="font-medium">{story.title}</h3>
                        <Badge variant="outline" className="mt-1">{story.industry}</Badge>
                      </div>
                      <div className="flex gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <Search className="h-4 w-4 mr-1" /> {story.views}
                        </div>
                        <div className="flex items-center">
                          <FileCheck className="h-4 w-4 mr-1" /> {story.downloads}
                        </div>
                        <div className="flex items-center">
                          <ExternalLink className="h-4 w-4 mr-1" /> {story.shares}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Content Optimization</CardTitle>
              <CardDescription>
                Recommendations to improve searchability and usefulness
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="border-mobio-blue/20 bg-mobio-lavender/20">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Tag Consistency</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <p className="text-sm">
                        Some similar technologies are tagged inconsistently:
                      </p>
                      <ul className="list-disc list-inside space-y-1 text-sm pl-2">
                        <li>"AI" vs "Artificial Intelligence"</li>
                        <li>"ML" vs "Machine Learning"</li>
                        <li>"IoT" vs "Internet of Things"</li>
                      </ul>
                      <Button size="sm" className="bg-mobio-blue hover:bg-mobio-blue/90" asChild>
                        <Link to="/master-data">
                          Standardize Tags
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>

                  <Card className="border-mobio-blue/20 bg-mobio-lavender/20">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Keyword Enhancement</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <p className="text-sm">
                        4 stories need improved keywords for better search results:
                      </p>
                      <div className="p-2 bg-white/50 rounded-md text-sm">
                        <div className="flex justify-between items-center">
                          <span>Global Insurance Digital Transformation</span>
                          <Button size="sm" variant="outline" 
                            onClick={() => handleApplyFix("keyword")}
                          >
                            AI Fix
                          </Button>
                        </div>
                      </div>
                      <Button size="sm" variant="link" className="p-0 mt-1" asChild>
                        <Link to="/repository">
                          View All Suggestions
                          <ArrowRight className="ml-1 h-3 w-3" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">AI-Generated Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-start gap-3 p-3 bg-secondary rounded-md">
                      <TrendingUp className="h-5 w-5 text-mobio-blue mt-0.5" />
                      <div>
                        <p className="font-medium mb-1">Trending Topic Opportunity</p>
                        <p className="text-sm text-muted-foreground mb-2">
                          Recent search trends show high interest in "GenAI for Customer Service" but your content is limited.
                          Consider adding 2-3 new success stories in this area.
                        </p>
                        <Button size="sm" variant="outline" onClick={() => handleApplyFix("ai suggestion")}>
                          <CheckCircle className="h-3 w-3 mr-1" /> Apply Recommendation
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 p-3 bg-secondary rounded-md">
                      <TagIcon className="h-5 w-5 text-mobio-blue mt-0.5" />
                      <div>
                        <p className="font-medium mb-1">Tag Consolidation</p>
                        <p className="text-sm text-muted-foreground mb-2">
                          The system detected 3 technology tags with similar meaning. Consolidating these would improve search accuracy.
                        </p>
                        <Button size="sm" variant="outline" onClick={() => handleApplyFix("tag consolidation")}>
                          <CheckCircle className="h-3 w-3 mr-1" /> Apply Recommendation
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 p-3 bg-secondary rounded-md">
                      <Search className="h-5 w-5 text-mobio-blue mt-0.5" />
                      <div>
                        <p className="font-medium mb-1">Search Patterns Analysis</p>
                        <p className="text-sm text-muted-foreground mb-2">
                          Sales team members frequently search by combining region and industry, but 40% of stories lack proper region tagging.
                        </p>
                        <Button size="sm" variant="outline" onClick={() => handleApplyFix("region tagging")}>
                          <CheckCircle className="h-3 w-3 mr-1" /> Apply Recommendation
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Content Quality Score</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-4">
                      <div className="w-24 h-24 rounded-full border-8 border-mobio-blue flex items-center justify-center">
                        <span className="text-2xl font-bold">82%</span>
                      </div>
                      <div className="flex-1 space-y-4">
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm font-medium">Completeness</span>
                            <span className="text-sm text-muted-foreground">90%</span>
                          </div>
                          <Progress value={90} className="h-1.5" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm font-medium">Tagging Quality</span>
                            <span className="text-sm text-muted-foreground">76%</span>
                          </div>
                          <Progress value={76} className="h-1.5" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm font-medium">Searchability</span>
                            <span className="text-sm text-muted-foreground">85%</span>
                          </div>
                          <Progress value={85} className="h-1.5" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Intelligence;
