import storyService from "../services/story.service.js";

class StoryController {
  async createStory(req, res) {
    try {
      const storyData = {
        title: req.body?.title,
        executive_summary: req.body?.executive_summary,
        client_segment: req.body?.client_segment,
        oneline_summary: req.body?.oneline_summary,
        business_challenges: req.body?.business_challenges,
        success_story_detail: req.body?.success_story_detail,
        industries: req.body?.industries,
        regions: req.body?.regions,
        technologies: req.body?.technologies,
        outcomes: req.body?.outcomes,
        general_tags: req.body?.general_tags,
        owner_id: req.user?.id || "aa976ab6-6d9f-4109-8eea-47dad2d3ec93",
        status: req.body?.status || "draft",
        created_by: req.user?.id || "aa976ab6-6d9f-4109-8eea-47dad2d3ec93",
        updated_by: req.user?.id || "aa976ab6-6d9f-4109-8eea-47dad2d3ec93",
      };

      const story = await storyService.createStory(storyData);

      return res.status(201).json({
        message: "Success story created successfully",
        data: story,
        status: 201,
      });
    } catch (error) {
      console.error("Create story error:", error);
      return res.status(500).json({
        message: error.message,
        status: 500,
      });
    }
  }

  async updateStory(req, res) {
    try {
      const storyId = req.params.id;
      const storyData = {
        title: req.body.title,
        executive_summary: req.body.executive_summary,
        client_segment: req.body.client_segment,
        oneline_summary: req.body.oneline_summary,
        business_challenges: req.body.business_challenges,
        success_story_detail: req.body.success_story_detail,
        industries: req.body.industries,
        regions: req.body.regions,
        technologies: req.body.technologies,
        outcomes: req.body.outcomes,
        general_tags: req.body.general_tags,
        status: req.body.status,
        image_assets: req.body.image_assets,
        audio_assets: req.body.audio_assets,
        video_assets: req.body.video_assets,
        document_assets: req.body.document_assets,
        demo_assets: req.body.demo_assets,
        updated_by: req.user?.id || "aa976ab6-6d9f-4109-8eea-47dad2d3ec93",
        publicAppearance: req.body.publicAppearance,
      };

      const story = await storyService.updateStory(storyId, storyData);

      return res.status(200).json({
        message: "Success story updated successfully",
        data: story,
        status: 200,
      });
    } catch (error) {
      console.error("Update story error:", error);
      return res.status(500).json({
        message: error.message,
        status: 500,
      });
    }
  }

  async getStories(req, res) {
    try {
      const { 
        page = 1, 
        limit = 10, 
        status, 
        search,
        title,
        industries,
        regions,
        technologies,
        outcomes,
        general_tags 
      } = req.query;
      const offset = (page - 1) * limit;

      // Parse filter arrays if present
      const filters = {
        industries,
        regions,
        technologies,
        outcomes,
        general_tags,
        title
      };

      // First get the filtered stories without count
      const stories = await storyService.getStories({
        page,
        limit,
        offset,
        status,
        search,
        filters,
        userId: req.user?.id || "aa976ab6-6d9f-4109-8eea-47dad2d3ec93",
      });

      // Get total count separately to avoid aggregate function issue
      const totalCount = await storyService.getStoriesCount({
        status,
        search,
        filters,
        userId: req.user?.id || "aa976ab6-6d9f-4109-8eea-47dad2d3ec93",
      });

      return res.status(200).json({
        message: "Success stories retrieved successfully",
        data: stories,
        total: totalCount,
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalCount / limit),
        status: 200,
      });
    } catch (error) {
      console.error("Get stories error:", error);
      return res.status(500).json({
        message: error.message,
        status: 500,
      });
    }
  }

  async getStoryById(req, res) {
    try {
      const storyId = req.params.id;
      const story = await storyService.getStoryById(storyId);

      if (!story) {
        return res.status(404).json({
          message: "Story not found",
          status: 404,
        });
      }

      return res.status(200).json({
        message: "Success story retrieved successfully",
        data: story,
        status: 200,
      });
    } catch (error) {
      console.error("Get story by id error:", error);
      return res.status(500).json({
        message: error.message,
        status: 500,
      });
    }
  }

  async deleteStory(req, res) {
    try {
      const storyId = req.params.id;
      await storyService.deleteStory(storyId, req.user.id);

      return res.status(200).json({
        message: "Success story deleted successfully",
        status: 200,
      });
    } catch (error) {
      console.error("Delete story error:", error);
      return res.status(500).json({
        message: error.message,
        status: 500,
      });
    }
  }
}

export default new StoryController();
