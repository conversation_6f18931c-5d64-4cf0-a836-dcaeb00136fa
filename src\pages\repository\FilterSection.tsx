
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter, RefreshCw, ChevronDown } from "lucide-react";
import { useGroupedTags } from "@/hooks/useGroupedTags";

interface FilterSectionProps {
  filters: {
    industry: string[];
    region: string[];
  };
  onFilterChange: (field: "industry" | "region", value: string) => void;
  onResetFilters: () => void;
  searchTerm: string;
}

const FilterSection = ({ 
  filters, 
  onFilterChange, 
  onResetFilters,
  searchTerm 
}: FilterSectionProps) => {
  const [isFiltersVisible, setIsFiltersVisible] = useState(false);
  const { data: tagGroups } = useGroupedTags();

  const isResetDisabled = !searchTerm && filters.industry.length === 0 && filters.region.length === 0;

  const getTagsByType = (type: string) => {
    return tagGroups?.find(g => g.type === type)?.tags || [];
  };

  return (
    <>
      <div className="flex gap-2">
        <Button
          variant="outline"
          className="gap-2"
          onClick={() => setIsFiltersVisible(!isFiltersVisible)}
        >
          <Filter className="h-4 w-4" />
          Filters
          <ChevronDown className={`h-4 w-4 transition-transform ${isFiltersVisible ? "rotate-180" : ""}`} />
        </Button>

        <Button
          variant="ghost"
          size="icon"
          onClick={onResetFilters}
          disabled={isResetDisabled}
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>

      {isFiltersVisible && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-secondary rounded-md fade-in">
          <div>
            <Label htmlFor="industry-filter">Industry</Label>
            <Select
              value={filters.industry[0] || "all"}
              onValueChange={(value) => onFilterChange("industry", value)}
            >
              <SelectTrigger id="industry-filter" className="mt-1">
                <SelectValue>
                  {filters.industry[0] ? 
                    getTagsByType("industry").find(t => t.id === filters.industry[0])?.name || "All Industries" 
                    : "All Industries"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Industries</SelectItem>
                {getTagsByType("industry").map((tag) => (
                  <SelectItem key={tag.id} value={tag.id}>
                    {tag.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="region-filter">Region</Label>
            <Select
              value={filters.region[0] || "all"}
              onValueChange={(value) => onFilterChange("region", value)}
            >
              <SelectTrigger id="region-filter" className="mt-1">
                <SelectValue>
                  {filters.region[0] ? 
                    getTagsByType("region").find(t => t.id === filters.region[0])?.name || "All Regions" 
                    : "All Regions"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Regions</SelectItem>
                {getTagsByType("region").map((tag) => (
                  <SelectItem key={tag.id} value={tag.id}>
                    {tag.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </>
  );
};

export default FilterSection;
