import documentService from "../services/document.service.js";

class DocumentController {
  async processContent(req, res, next) {
    try {
      let content;
      let n8nResponse;
      
      if (req.file) {
        content = await documentService.extractContent(
          req.file,
          req.body.description
        );
        n8nResponse = await documentService.sendToN8n(content);
      } 
      // Handle URL
      else if (req.body.url) {
        n8nResponse = await documentService.sendToN8n("", req.body.url);
      }
      // Handle description only
      else if (req.body.description) {
        content = req.body.description;
        n8nResponse = await documentService.sendToN8n(content);
      } 
      // No content provided
      else {
        return res.status(400).json({
          message: "No content provided. Please provide a file, URL, or description.",
          status: 400
        });
      }

      // Return the n8n workflow response
      return res.status(200).json({
        message: content,
        n8nResponse,
        status: 200
      });
    } catch (error) {
      console.error("Processing error:", error);
      return res.status(500).json({
        message: error.message,
        status: 500
      });
    }
  }
}

export default new DocumentController();