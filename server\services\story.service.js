import { supabase } from "../config/db.js";

class StoryService {
  async createStory(storyData) {
    const { data, error } = await supabase
      .from("success_stories")
      .insert([
        {
          title: storyData.title,
          executive_summary: storyData.executive_summary,
          client_segment: storyData.client_segment,
          oneline_summary: storyData.oneline_summary,
          business_challenges: storyData.business_challenges,
          success_story_detail: storyData.success_story_detail,
          industries: storyData.industries,
          regions: storyData.regions,
          technologies: storyData.technologies,
          outcomes: storyData.outcomes,
          general_tags: storyData.general_tags,
          owner_id: storyData.owner_id,
          status: storyData.status,
          created_by: storyData.created_by,
          updated_by: storyData.updated_by,
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateStory(storyId, storyData) {
    const { data, error } = await supabase
      .from("success_stories")
      .update({
        title: storyData.title,
        executive_summary: storyData.executive_summary,
        client_segment: storyData.client_segment,
        oneline_summary: storyData.oneline_summary,
        business_challenges: storyData.business_challenges,
        success_story_detail: storyData.success_story_detail,
        industries: storyData.industries,
        regions: storyData.regions,
        technologies: storyData.technologies,
        outcomes: storyData.outcomes,
        general_tags: storyData.general_tags,
        status: storyData.status,
        image_assets: storyData.image_assets,
        audio_assets: storyData.audio_assets,
        video_assets: storyData.video_assets,
        document_assets: storyData.document_assets,
        demo_assets: storyData.demo_assets,
        updated_by: storyData.updated_by,
        publicAppearance: storyData.publicAppearance,
        updated_at: new Date().toISOString(),
      })
      .eq("id", storyId)
      .eq("flag_deleted", false)
      .select()
      .single();

    if (error) throw error;
    if (!data) throw new Error("Story not found or already deleted");
    return data;
  }

  async getStories({ page, limit, offset, status, search, filters, userId }) {
    let baseQuery = supabase
      .from("success_stories")
      .select(
        `
        *,
        owner:owner_id(name)
      `
      )
      .eq("flag_deleted", false);

    if (status) {
      baseQuery = baseQuery.eq("status", status);
    }

    if (search) {
      baseQuery = baseQuery.or(`
        title.ilike.%${search}%,
        executive_summary.ilike.%${search}%,
        client_segment.ilike.%${search}%,
        oneline_summary.ilike.%${search}%
      `);
    }

    // Add title filter
    if (filters.title) {
      baseQuery = baseQuery.ilike('title', `%${filters.title}%`);
    }

    // Add filters for each category
    if (filters.industries?.length > 0) {
      baseQuery = baseQuery.contains('industries', filters.industries);
    }

    if (filters.regions?.length > 0) {
      baseQuery = baseQuery.contains('regions', filters.regions);
    }

    if (filters.technologies?.length > 0) {
      baseQuery = baseQuery.contains('technologies', filters.technologies);
    }

    if (filters.outcomes?.length > 0) {
      baseQuery = baseQuery.contains('outcomes', filters.outcomes);
    }

    if (filters.general_tags?.length > 0) {
      baseQuery = baseQuery.contains('general_tags', filters.general_tags);
    }

    const { data, error } = await baseQuery
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return {
      rows: data.map((row) => ({
        ...row,
        owner_name: row.owner?.name,
      })),
      count: data.length,
    };
  }

  async getStoriesCount({ status, search }) {
    let countQuery = supabase
      .from("success_stories")
      .select("id", { count: "exact" })
      .eq("flag_deleted", false);

    if (status) {
      countQuery = countQuery.eq("status", status);
    }

    if (search) {
      countQuery = countQuery.or(`
        title.ilike.%${search}%,
        executive_summary.ilike.%${search}%,
        client_segment.ilike.%${search}%,
        oneline_summary.ilike.%${search}%
      `);
    }

    const { count, error } = await countQuery;

    if (error) throw error;
    return count || 0;
  }

  async getStoryById(storyId) {
    const { data, error } = await supabase
      .from("success_stories")
      .select(
        `
        *,
        owner:owner_id(name),
        creator:created_by(name),
        updater:updated_by(name)
      `
      )
      .eq("id", storyId)
      .eq("flag_deleted", false)
      .single();

    if (error) throw error;

    return data
      ? {
          ...data,
          owner_name: data.owner?.name,
          creator_name: data.creator?.name,
          updater_name: data.updater?.name,
        }
      : null;
  }

  async deleteStory(storyId, userId) {
    const { data, error } = await supabase
      .from("success_stories")
      .update({
        flag_deleted: true,
        updated_by: userId,
        updated_at: new Date().toISOString(),
      })
      .eq("id", storyId)
      .eq("flag_deleted", false)
      .select()
      .single();

    if (error) throw error;
    if (!data) throw new Error("Story not found or already deleted");
    return data;
  }
}

export default new StoryService();
