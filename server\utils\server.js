import { logger } from "./logger.js";

export const setupGracefulShutdown = (server) => {
  // Handle graceful shutdown
  const gracefulShutdown = () => {
    logger.info('Received shutdown signal. Starting graceful shutdown...');
    
    server.close(() => {
      logger.info('Server closed. Process terminating...');
      process.exit(0);
    });

    // Force shutdown after 10 seconds if server hasn't closed
    setTimeout(() => {
      logger.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  };

  // Listen for termination signals
  process.on('SIGTERM', gracefulShutdown);
  process.on('SIGINT', gracefulShutdown);
};