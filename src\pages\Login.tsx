
import React, { useState } from "react";
import { Navigate, useLocation, Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, UserCircle, Users } from "lucide-react";

const Login = () => {
  const { login, isAuthenticated, isAdmin, isLoading } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const location = useLocation();

  // Redirect to appropriate page if already authenticated
  if (isAuthenticated && !isLoading) {
    // Check if there's a redirect path in the state
    const from = (location.state as { from?: { pathname?: string } })?.from?.pathname || (isAdmin ? "/admin" : "/");
    return <Navigate to={from} replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      await login(email, password);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleQuickLogin = async (type: 'admin' | 'user') => {
    setIsSubmitting(true);
    const credentials = {
      admin: { email: "<EMAIL>", password: "password@123" },
      user: { email: "<EMAIL>", password: "user123" } // Assuming user demo creds remain or should be updated if provided
    };
    
    setEmail(credentials[type].email);
    setPassword(credentials[type].password);
    
    try {
      await login(credentials[type].email, credentials[type].password);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-blue-50 to-slate-100 p-4">
      <div className="w-full max-w-md">
        <div className="flex justify-center items-center mb-8">
          <img 
            src="/lovable-uploads/apple-touch-icon.png" 
            alt="WinWise Logo" 
            className="h-12" 
          />
          <span className="font-poppins font-semibold text-mobio-blue text-3xl ml-2 align-middle">WinWise</span>
        </div>
        
        <Card className="border-mobio-lavender/30 shadow-lg">
          <CardHeader className="space-y-1 bg-mobio-lavender/10 rounded-t-lg">
            <CardTitle className="text-2xl text-center text-mobio-blue">Welcome Back</CardTitle>
            <CardDescription className="text-center">
              Enter your credentials to access the platform
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pt-6">
            {/* Quick login buttons */}
            <div className="flex gap-3 mb-6">
              <Button 
                className="flex-1 bg-mobio-blue hover:bg-mobio-blue/90"
                onClick={() => handleQuickLogin('admin')}
                disabled={isSubmitting}
              >
                <UserCircle className="mr-2 h-4 w-4" />
                Login as Admin
              </Button>
              <Button 
                className="flex-1 bg-mobio-lavender hover:bg-mobio-lavender/90"
                onClick={() => handleQuickLogin('user')}
                disabled={isSubmitting}
              >
                <Users className="mr-2 h-4 w-4" />
                Login as User
              </Button>
            </div>
            
            <div className="relative my-4">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t"></span>
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  autoComplete="email"
                  className="bg-slate-50"
                />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <button type="button" className="text-xs text-mobio-blue hover:underline">
                    Forgot password?
                  </button>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="bg-slate-50"
                />
              </div>
              
              <Button 
                type="submit" 
                className="w-full bg-mobio-blue hover:bg-mobio-blue/90"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing In...
                  </>
                ) : (
                  "Sign In"
                )}
              </Button>
            </form>
          </CardContent>
          
          <CardFooter className="flex flex-col space-y-4 border-t pt-4">
            <div className="text-xs text-center text-muted-foreground">
              <p className="mb-2">Demo Credentials:</p>
              <p><strong>Admin:</strong> <EMAIL> / password@123</p>
              <p><strong>User:</strong> <EMAIL> / user123</p>
            </div>
            <p className="text-sm text-muted-foreground">
              Don't have an account?{" "}
              <Link to="/register" className="text-mobio-blue hover:underline">
                Sign up
              </Link>
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Login;
