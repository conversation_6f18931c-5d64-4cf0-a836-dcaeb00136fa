import express from "express";
import authController from "../controller/auth.controller.js";
import { authenticateToken } from "../middleware/auth.js";

const router = express.Router();

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post("/register", authController.register);

// @route   POST /api/auth/login
// @desc    Login user and return token
// @access  Public
router.post("/login", authController.login);

// @route   GET /api/auth/me
// @desc    Get current user profile
// @access  Private
router.get("/me", authenticateToken, authController.getProfile);

// @route   GET /api/auth/users
// @desc    Get all users (not deleted)
// @access  Private (Admin only)
router.get("/users", authenticateToken, authController.getAllUsers);

// @route   PUT /api/auth/users/:id
// @desc    Update user
// @access  Private (Admin only)
router.put("/users/:id", authenticateToken, authController.updateUser);

// @route   DELETE /api/auth/users/:id
// @desc    Soft delete user
// @access  Private (Admin only)
router.delete("/users/:id", authenticateToken, authController.deleteUser);

// @route   GET /api/auth/users/:id
// @desc    Get user by ID
// @access  Private
router.get("/users/:id", authenticateToken, authController.getUserById);

// @route   POST /api/auth/logout
// @desc    Logout user and invalidate token on server
// @access  Private (requires an active session token)
router.post("/logout", authenticateToken, authController.logout);

// @route   POST /api/auth/refresh-token
// @desc    Refresh access token using a refresh token
// @access  Public (authorization is via the refresh token itself)
router.post("/refresh-token", authController.refreshToken);

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Private
router.put("/profile", authenticateToken, authController.updateProfile);

export default router;

