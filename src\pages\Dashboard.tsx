
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Link } from "react-router-dom";

import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";

import {
  ArrowUp,
  ArrowUpRight,
  FileText,
  BarChart2,
  Pie<PERSON>hart as PieChartIcon,
  PlusCircle,
  Search,
  RefreshCw,
  MoveRight,
  AlertTriangle,
  Video,
  Image as ImageIcon,
  Headphones,
} from "lucide-react";

// Mock data for recent stories
const recentStories = [
  {
    id: "1",
    title: "AI-Powered Inventory Management",
    date: "2023-05-10",
    views: 142,
    trend: "up",
    tags: ["Retail", "AI", "APAC"],
    hasVideo: true,
    hasAudio: false,
    hasPdf: true,
    hasImage: true,
  },
  {
    id: "2",
    title: "Healthcare Patient Portal Solution",
    date: "2023-05-08",
    views: 98,
    trend: "up",
    tags: ["Healthcare", "UX", "US"],
    hasVideo: true,
    hasAudio: true,
    hasPdf: true,
    hasImage: false,
  },
  {
    id: "3",
    title: "Banking Security Enhancement",
    date: "2023-05-05",
    views: 76,
    trend: "down",
    tags: ["FinTech", "Security", "Global"],
    hasVideo: false,
    hasAudio: false,
    hasPdf: true,
    hasImage: true,
  },
  {
    id: "4",
    title: "Supply Chain Optimization",
    date: "2023-05-02",
    views: 115,
    trend: "up",
    tags: ["Manufacturing", "Logistics", "Europe"],
    hasVideo: false,
    hasAudio: true,
    hasPdf: true,
    hasImage: true,
  },
];

// Mock data for industry distribution
const industryData = [
  { name: "Healthcare", value: 27 },
  { name: "Retail", value: 19 },
  { name: "FinTech", value: 15 },
  { name: "Manufacturing", value: 12 },
  { name: "Logistics", value: 8 },
];

// Mock data for chart
const monthlyData = [
  { name: "Jan", uploads: 4, views: 28 },
  { name: "Feb", uploads: 7, views: 43 },
  { name: "Mar", uploads: 5, views: 32 },
  { name: "Apr", uploads: 10, views: 65 },
  { name: "May", uploads: 15, views: 92 },
];

// Colors for pie chart
const COLORS = ["#1565C0", "#42A5F5", "#90CAF9", "#E3F2FD", "#BBDEFB"];

// Function to format large numbers
const formatNumber = (num: number) => {
  return num > 999 ? `${(num / 1000).toFixed(1)}k` : num;
};

const Dashboard = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">
            Overview of WinWise Success Story management
          </p>
        </div>
        <Button asChild>
          <Link to="/admin/add-story" className="flex items-center gap-1">
            <PlusCircle className="h-4 w-4" /> Add New Story
          </Link>
        </Button>
      </div>
      
      {/* KPI Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Success Stories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">124</div>
            <div className="text-xs text-muted-foreground mt-1">
              <span className="text-green-600 inline-flex items-center">
                <ArrowUp className="h-3 w-3 mr-1" /> 12% from last month
              </span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Monthly Views
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.8k</div>
            <div className="text-xs text-muted-foreground mt-1">
              <span className="text-green-600 inline-flex items-center">
                <ArrowUp className="h-3 w-3 mr-1" /> 18% from last month
              </span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Industries Covered
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <div className="text-xs text-muted-foreground mt-1">
              <span className="text-green-600 inline-flex items-center">
                <ArrowUp className="h-3 w-3 mr-1" /> 2 new industries added
              </span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Completion Rate
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="text-2xl font-bold">86%</div>
            <Progress value={86} className="h-2" />
          </CardContent>
        </Card>
      </div>
      
      {/* Main Content */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
        {/* Charts */}
        <Card className="lg:col-span-4">
          <Tabs defaultValue="uploads" className="w-full">
            <CardHeader className="pb-0">
              <div className="flex items-center justify-between">
                <CardTitle>Story Metrics</CardTitle>
                <TabsList>
                  <TabsTrigger value="uploads" className="text-xs">Uploads</TabsTrigger>
                  <TabsTrigger value="views" className="text-xs">Views</TabsTrigger>
                </TabsList>
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              <TabsContent value="uploads" className="mt-0">
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart
                    data={monthlyData}
                    margin={{
                      top: 5,
                      right: 5,
                      left: 5,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="uploads" name="Story Uploads" fill="#1565C0" />
                  </BarChart>
                </ResponsiveContainer>
              </TabsContent>
              <TabsContent value="views" className="mt-0">
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart
                    data={monthlyData}
                    margin={{
                      top: 5,
                      right: 5,
                      left: 5,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="views" name="Story Views" fill="#E53935" />
                  </BarChart>
                </ResponsiveContainer>
              </TabsContent>
            </CardContent>
          </Tabs>
        </Card>
        
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle>Industry Distribution</CardTitle>
          </CardHeader>
          <CardContent className="flex justify-center">
            <ResponsiveContainer width="100%" height={220}>
              <PieChart>
                <Pie
                  data={industryData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {industryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => `${value} stories`} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        {/* Recent Stories */}
        <Card className="lg:col-span-4">
          <CardHeader className="pb-3">
            <CardTitle>Recent Stories</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-72">
              <ul className="space-y-4">
                {recentStories.map((story) => (
                  <li key={story.id} className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h4 className="text-sm font-medium">{story.title}</h4>
                      <div className="flex flex-wrap gap-1">
                        {story.tags.map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs py-0 h-5">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex gap-1 items-center text-muted-foreground text-xs">
                        {story.hasPdf && <FileText className="h-3 w-3" />}
                        {story.hasVideo && <Video className="h-3 w-3" />}
                        {story.hasAudio && <Headphones className="h-3 w-3" />}
                        {story.hasImage && <ImageIcon className="h-3 w-3" />}
                      </div>
                    </div>
                    <div className="text-sm text-right">
                      <div className="font-medium flex items-center justify-end">
                        {formatNumber(story.views)} views
                        {story.trend === "up" ? (
                          <ArrowUpRight className="h-3 w-3 text-green-600 ml-1" />
                        ) : (
                          <ArrowUpRight className="h-3 w-3 text-red-600 ml-1 rotate-90" />
                        )}
                      </div>
                      <div className="text-muted-foreground text-xs">{story.date}</div>
                    </div>
                  </li>
                ))}
              </ul>
            </ScrollArea>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="ghost" size="sm" asChild className="w-full">
              <Link to="/admin/repository" className="flex items-center justify-center">
                View All Stories <MoveRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
        
        {/* Insights Card */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle>Admin Intelligence</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-72">
              <div className="space-y-4">
                <div className="p-3 bg-yellow-50 border border-yellow-100 rounded-md flex gap-3">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 flex-shrink-0" />
                  <div>
                    <h5 className="text-sm font-medium text-yellow-800">Missing Metadata</h5>
                    <p className="text-xs text-yellow-700">
                      5 stories are missing key industry tags and 3 lack region information
                    </p>
                  </div>
                </div>
                
                <div>
                  <h5 className="text-sm font-medium mb-2">Top Search Queries</h5>
                  <ul className="space-y-2">
                    <li className="flex justify-between text-sm">
                      <span>"Healthcare AI solutions"</span>
                      <span className="text-muted-foreground">24 searches</span>
                    </li>
                    <li className="flex justify-between text-sm">
                      <span>"Retail in APAC"</span>
                      <span className="text-muted-foreground">19 searches</span>
                    </li>
                    <li className="flex justify-between text-sm">
                      <span>"Supply chain optimization"</span>
                      <span className="text-muted-foreground">16 searches</span>
                    </li>
                  </ul>
                </div>
                
                <Separator />
                
                <div>
                  <h5 className="text-sm font-medium mb-2">Recommended Actions</h5>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2 text-sm">
                      <PlusCircle className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                      <span>Add more Financial Services stories (high demand, low supply)</span>
                    </li>
                    <li className="flex items-start gap-2 text-sm">
                      <RefreshCw className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                      <span>Update metadata for Europe region success stories</span>
                    </li>
                    <li className="flex items-start gap-2 text-sm">
                      <Search className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                      <span>Review search terms with zero results to identify content gaps</span>
                    </li>
                  </ul>
                </div>
              </div>
            </ScrollArea>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="ghost" size="sm" asChild className="w-full">
              <Link to="/admin/intelligence" className="flex items-center justify-center">
                View Full Intelligence <MoveRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
