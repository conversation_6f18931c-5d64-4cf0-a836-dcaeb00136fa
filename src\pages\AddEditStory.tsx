import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { StoryProvider, useStory } from "./AddEditStory/StoryContext";
import { BasicInformation } from "./AddEditStory/BasicInformation";
import { Categorization } from "./AddEditStory/Categorization";
import { Attachments } from "./AddEditStory/Attachments";
import { AiContentGenerator } from "./AddEditStory/AiContentGenerator";
import { SidebarComponents } from "./AddEditStory/Sidebar";
import { useStoryApi } from "@/hooks/useStoryApi";
import { toast } from "@/components/ui/use-toast";

// Constants
const TAB_CONFIG = [
  { value: "basic", label: "Basic Information" },
  { value: "category", label: "Categorization" },
  { value: "attachments", label: "Attachments" },
];

const STATUS_COLORS = {
  published: "text-green-600",
  draft: "text-yellow-600",
  archived: "text-gray-400",
};

// Spinner
const LoadingSpinner = () => (
  <div className="flex h-[80vh] items-center justify-center">
    <div className="flex flex-col items-center gap-2">
      <Loader2 className="h-8 w-8 animate-spin text-mobio-blue" />
      <p className="text-sm text-muted-foreground">Loading story data...</p>
    </div>
  </div>
);

// Header
const StoryHeader = ({
  isEditMode,
  storyData,
  isSaving,
  onSave,
  onPublish,
  localStatus,
  setLocalStatus,
  isSavingDraft,
  isBasicInfoCompleted, // <-- Add this prop
}: {
  isEditMode: boolean;
  storyData: any;
  isSaving: boolean;
  onSave: () => void;
  onPublish: () => void;
  localStatus: "draft" | "published" | "archived";
  setLocalStatus: (value: "draft" | "published" | "archived") => void;
  isSavingDraft: boolean;
  isBasicInfoCompleted: boolean; // <-- Add this prop
}) => (
  <div className="flex justify-between items-center mb-6">
    <div>
      <h1 className="text-2xl font-semibold text-mobio-blue mb-1">
        {isEditMode ? "Edit Success Story" : "Create New Success Story"}
      </h1>
      <p className="text-muted-foreground">
        {isEditMode
          ? `Editing story: ${storyData?.title || "Untitled"}`
          : "Fill out the details to create a new success story"}
      </p>
    </div>

    <div className="flex gap-3">
      {isEditMode && (
        <Select
          value={localStatus}
          onValueChange={(value) => setLocalStatus(value as typeof localStatus)}
        >
          <SelectTrigger
            className={`w-[180px] ${
              STATUS_COLORS[localStatus] || "text-gray-600"
            }`}
          >
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.keys(STATUS_COLORS).map((status) => (
              <SelectItem
                key={status}
                value={status}
                className={STATUS_COLORS[status]}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
      {!isEditMode && (
        <Button
          variant="outline"
          onClick={onSave}
          disabled={!isBasicInfoCompleted || isSavingDraft || isSaving} // <-- Disable until completed
        >
          {isSavingDraft ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
            </>
          ) : (
            "Save Draft"
          )}
        </Button>
      )}
      <Button
        onClick={onPublish}
        disabled={isSaving || (!isEditMode && !isBasicInfoCompleted)}
      >
        {isSaving ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {isEditMode ? "Updating..." : "Publishing..."}
          </>
        ) : isEditMode ? (
          "Update"
        ) : (
          "Publish"
        )}
      </Button>
    </div>
  </div>
);

// Main Page
const AddEditStory = () => {
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState("basic");

  return (
    <StoryProvider editId={id}>
      <AddEditStoryContent
        isEditMode={!!id}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
      />
    </StoryProvider>
  );
};

// Content
const AddEditStoryContent = ({
  isEditMode,
  activeTab,
  setActiveTab,
}: {
  isEditMode: boolean;
  activeTab: string;
  setActiveTab: (tab: string) => void;
}) => {
  const navigate = useNavigate();
  const { storyData, isLoading, saveStory, isSaving, storyResponse } =
    useStory();
  const { updateStory } = useStoryApi();
  const [isBasicInfoCompleted, setIsBasicInfoCompleted] = useState(false);
  const [localStatus, setLocalStatus] = useState<
    "draft" | "published" | "archived"
  >(storyData?.status || "draft");
  const [isSavingDraft, setIsSavingDraft] = useState(false);

  useEffect(() => {
    if (storyData?.status) {
      setLocalStatus(storyData.status);
    }
  }, [storyData?.status]);

  if (isLoading) return <LoadingSpinner />;

  const handlePublish = async () => {
    try {
      const statusToSave = isEditMode ? localStatus : "published";
      const storyId = storyResponse?.data?.id || storyData?.id;

      await updateStory(storyId, { status: statusToSave });

      await saveStory(statusToSave);
      navigate("/admin/repository");
      toast({ title: "Success", description: "Story published successfully" });
    } catch (error) {
      console.error("Error publishing story:", error);
      toast({
        title: "Error",
        description: "Failed to publish story",
        variant: "destructive",
      });
    }
  };

  const handleDraft = async () => {
    setIsSavingDraft(true);
    try {
      const storyId = storyResponse?.data?.id || storyData?.id;
      if (storyId) {
        await updateStory(storyId, { status: "draft" });
        await saveStory("draft");
        navigate("/admin/repository");
        toast({ title: "Success", description: "Story saved as draft" });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save draft",
        variant: "destructive",
      });
    } finally {
      setIsSavingDraft(false);
    }
  };

  const renderTabContent = (value: string) => {
    switch (value) {
      case "basic":
        return (
          <BasicInformation
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            onComplete={() => {
              setIsBasicInfoCompleted(true);
              setActiveTab('category');
            }}
            iseditMode={isEditMode}
            editStoryData={storyData}
          />
        );
      case "category":
        return <Categorization setActiveTab={setActiveTab} />;
      case "attachments":
        return <Attachments />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <StoryHeader
        isEditMode={isEditMode}
        storyData={storyData}
        isSaving={isSaving}
        localStatus={localStatus}
        setLocalStatus={setLocalStatus}
        onSave={handleDraft}
        isSavingDraft={isSavingDraft}
        onPublish={handlePublish}
        isBasicInfoCompleted={isBasicInfoCompleted} // <-- Pass down
      />

      <AiContentGenerator />

      <div className="flex flex-col md:flex-row gap-6">
        <div className="flex-1">
          <Tabs
            value={activeTab}
            onValueChange={(value) => {
              if (isBasicInfoCompleted || value === "basic") setActiveTab(value);
            }}
          >
            <TabsList className="w-full bg-slate-100 p-0 h-auto mb-6">
              {TAB_CONFIG.map(({ value, label }) => (
                <TabsTrigger
                  key={value}
                  value={value}
                  disabled={!isBasicInfoCompleted && value !== "basic"}
                  className="flex-1 py-3 data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-mobio-blue disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {label}
                </TabsTrigger>
              ))}
            </TabsList>

            {TAB_CONFIG.map(({ value }) => (
              <TabsContent key={value} value={value} className="mt-0">
                {renderTabContent(value)}
              </TabsContent>
            ))}
          </Tabs>
        </div>

        <aside className="w-full md:w-64 lg:w-80">
          <SidebarComponents />
        </aside>
      </div>
    </div>
  );
};

export default AddEditStory;
