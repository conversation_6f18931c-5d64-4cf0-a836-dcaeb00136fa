import { useCallback, useMemo, useState } from "react";
import {
  FileIcon,
  ImageIcon,
  FileTextIcon,
  X as XIcon,
  Upload as UploadIcon,
  Video as VideoIcon,
  FileAudio,
  ExternalLink,
  Plus,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useStory } from "./StoryContext";
import { Attachment } from "./types";
import { Pencil } from "lucide-react"; // Import the new icon
import { Switch } from "@/components/ui/switch";
import { toast } from "react-toastify";
import { useMediaUpload } from "@/hooks/useMediaUpload";
import { useStoryApi } from "@/hooks/useStoryApi";

export const Attachments = () => {
  const {
    storyData,
    handleAddAttachment,
    handleRemoveAttachment,
    accessDetails,
    newAccessDetail,
    isAddingAccessDetail,
    handleAccessDetailChange,
    handleSaveAccessDetail,
    handleRemoveAccessDetail,
    handleEditAccessDetail,
    setIsAddingAccessDetail,
    storyResponse,
    setStoryResponse,
  } = useStory();

  const [activeTab, setActiveTab] = useState("image");
  const [dragActive, setDragActive] = useState(false);
  const [demoUrl, setDemoUrl] = useState("");
  const [urlInput, setUrlInput] = useState("");
  const [showCredentials, setShowCredentials] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { updateStory } = useStoryApi();

  const storyId = storyResponse?.data?.id;

  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files, activeTab);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files, activeTab);
    }
  };

  const { uploadAudio, uploadImage, uploadVideo, uploadDocument } =
    useMediaUpload(); // Remove isUploading since it's not used

  const handleSaveAttachments = async (type: string) => {
    const typeAttachments = storyData.attachments.filter(
      (att) => att.type === type
    );

    if (typeAttachments.length > 0) {
      setIsSaving(true);
      try {
        const urlAttachments = typeAttachments.filter(
          (att) => !att.url.startsWith("blob:")
        );
        const fileAttachments = typeAttachments.filter((att) =>
          att.url.startsWith("blob:")
        );

        // Get files from attachments that are blob URLs
        const files = await Promise.all(
          fileAttachments.map(async (att) => {
            const response = await fetch(att.url);
            if (!response.ok) {
              throw new Error(`Failed to fetch file: ${response.statusText}`);
            }
            const blob = await response.blob();
            return new File([blob], att.name, { type: `${type}/*` });
          })
        );

        let response: any;
        let payload: any;

        // Upload files and combine with URL attachments
        switch (type) {
          case "image":
            response =
              files.length > 0 ? await uploadImage(storyId, files) : [];
            payload = {
              image_assets: [
                ...(Array.isArray(response)
                  ? response.map((item: { json: any }) => item.json)
                  : []),
                ...urlAttachments.map((att) => ({
                  name: att.name,
                  url: att.url,
                  previewUrl: att.previewUrl,
                  type: "url",
                })),
              ],
            };
            break;
          case "video":
            response =
              files.length > 0 ? await uploadVideo(storyId, files) : [];
            payload = {
              video_assets: [
                ...(Array.isArray(response)
                  ? response.map((item: { json: any }) => item.json)
                  : []),
                ...urlAttachments.map((att) => ({
                  name: att.name,
                  url: att.url,
                  previewUrl: att.previewUrl,
                  type: "url",
                })),
              ],
            };
            break;
          case "document":
            response =
              files.length > 0 ? await uploadDocument(storyId, files) : [];
            payload = {
              document_assets: [
                ...(Array.isArray(response)
                  ? response.map((item: { json: any }) => item.json)
                  : []),
                ...urlAttachments.map((att) => ({
                  name: att.name,
                  url: att.url,
                  previewUrl: att.previewUrl,
                  type: "url",
                })),
              ],
            };
            break;
          case "audio":
            response =
              files.length > 0 ? await uploadAudio(storyId, files) : [];
            payload = {
              audio_assets: [
                ...(Array.isArray(response)
                  ? response.map((item: { json: any }) => item.json)
                  : []),
                ...urlAttachments.map((att) => ({
                  name: att.name,
                  url: att.url,
                  previewUrl: att.previewUrl,
                  type: "url",
                })),
              ],
            };
            break;
          default:
            throw new Error(`Unsupported file type: ${type}`);
        }

        const attachmentResponse = await updateStory(storyId, payload);
        setStoryResponse(attachmentResponse);

        toast.success(`${type} attachments saved successfully`);
      } catch (error) {
        console.error("Save Error:", error);
        toast.error(
          `Failed to save ${type}s: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      } finally {
        setIsSaving(false);
      }
    } else {
      toast.warning(`No ${type}s to save`);
    }
  };

  const createDemoPayload = (
    urlAttachments: Attachment[],
    accessDetails: any[]
  ) => {
    return {
      demo_assets: urlAttachments.map((att) => ({
        name: att.name,
        url: att.url,
        previewUrl: att.previewUrl,
        type: "url",
        credentials: accessDetails.map((detail) => ({
          role: detail.role,
          email: detail.email,
          password: detail.password,
        })),
      })),
    };
  };

  const handleSaveDemo = async () => {
    setIsSaving(true);
    try {
      if (!demoUrl) {
        toast.warning("Please enter a demo URL");
        return;
      }

      const demoAttachment = {
        id: storyId,
        name: "Demo URL",
        type: "demo",
        url: demoUrl,
        previewUrl: demoUrl,
      };

      const payload = createDemoPayload([demoAttachment], accessDetails);

      const attachmentResponse = await updateStory(storyId, payload);

      setStoryResponse(attachmentResponse);
      toast.success("Demo URL and credentials saved successfully");
    } catch (error) {
      console.error("Save Demo Error:", error);
      toast.error(
        `Failed to save demo: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setIsSaving(false);
    }
  };

  // Memoize filtered attachments
  const filteredAttachments = useMemo(
    () => storyData.attachments.filter((att) => att.type === activeTab),
    [storyData.attachments, activeTab]
  );

  // Memoize file icon getter
  const getFileIcon = useCallback((type: string) => {
    switch (type) {
      case "image":
        return <ImageIcon className="h-6 w-6 text-blue-500" />;
      case "audio":
        return <FileAudio className="h-6 w-6 text-green-500" />;
      case "video":
        return <VideoIcon className="h-6 w-6 text-red-500" />;
      case "document":
        return <FileTextIcon className="h-6 w-6 text-amber-500" />;
      default:
        return <FileIcon className="h-6 w-6 text-gray-500" />;
    }
  }, []);

  // Only declare handleAddUrl ONCE
  const handleAddUrl = useCallback(
    (type: string) => {
      if (urlInput.trim()) {
        const newAttachment: any = {
          id: storyId,
          name: urlInput.split("/").pop() || `${type}-url`,
          type: type,
          url: urlInput,
          previewUrl: urlInput,
        };
        handleAddAttachment(newAttachment);
        setUrlInput("");
        toast.success(`${type} URL added successfully`);
      }
    },
    [urlInput, storyId, handleAddAttachment]
  );

  // Only declare handleFiles ONCE
  const handleFiles = useCallback(
    async (files: FileList, type: string) => {
      try {
        Array.from(files).forEach((file) => {
          const newAttachment: Attachment = {
            id: storyId,
            name: file.name,
            type: type,
            url: URL.createObjectURL(file),
            previewUrl: URL.createObjectURL(file),
          };
          handleAddAttachment(newAttachment);
        });
      } catch (error) {
        console.error("Error creating attachments:", error);
        toast.error(`Failed to create ${type} attachments: ${error.message}`);
      }
    },
    [storyId, handleAddAttachment]
  );

  return (
    <Card className="border-mobio-lavender/40">
      <CardHeader className="bg-mobio-lavender/10">
        <CardTitle className="text-mobio-blue">Media & Resources</CardTitle>
        <CardDescription>
          Upload supporting files and provide demo access details
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-5">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6 grid grid-cols-5 bg-slate-100">
            <TabsTrigger
              value="image"
              className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              Images
            </TabsTrigger>
            <TabsTrigger
              value="audio"
              className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              Audio
            </TabsTrigger>
            <TabsTrigger
              value="video"
              className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              Video
            </TabsTrigger>
            <TabsTrigger
              value="document"
              className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              Documents
            </TabsTrigger>
            <TabsTrigger
              value="demo"
              className="data-[state=active]:bg-white data-[state=active]:shadow-sm"
            >
              Demo Access
            </TabsTrigger>
          </TabsList>

          {/* File Upload Tabs */}
          {activeTab !== "demo" && (
            <TabsContent value={activeTab} className="mt-0 space-y-4">
              <div
                className={`border-2 border-dashed rounded-lg p-6 text-center ${
                  dragActive
                    ? "border-mobio-blue bg-mobio-blue/5"
                    : "border-gray-300"
                }`}
                onDragEnter={handleDrag}
                onDragOver={handleDrag}
                onDragLeave={handleDrag}
                onDrop={handleDrop}
              >
                <div className="flex flex-col items-center justify-center space-y-2">
                  <UploadIcon className="h-8 w-8 text-muted-foreground" />
                  <div className="text-sm text-muted-foreground">
                    <Label
                      htmlFor={`${activeTab}-upload`}
                      className="relative cursor-pointer text-mobio-blue hover:underline"
                    >
                      Click to upload
                    </Label>{" "}
                    or drag and drop files
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {activeTab === "image" && "JPG, PNG, GIF up to 10MB"}
                    {activeTab === "audio" && "MP3, WAV files up to 20MB"}
                    {activeTab === "video" && "MP4, MOV files up to 50MB"}
                    {activeTab === "document" &&
                      "PDF, DOC, PPT files up to 10MB"}
                  </p>
                  <Input
                    id={`${activeTab}-upload`}
                    type="file"
                    multiple
                    accept={
                      activeTab === "image"
                        ? "image/*"
                        : activeTab === "audio"
                        ? "audio/*"
                        : activeTab === "video"
                        ? "video/*"
                        : activeTab === "document"
                        ? ".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx"
                        : undefined
                    }
                    className="hidden"
                    onChange={handleFileChange}
                  />
                </div>
              </div>

              {/* URL Input for Direct Links */}
              <div className="mt-4">
                <Label
                  htmlFor={`${activeTab}-url`}
                  className="text-base font-medium"
                >
                  Add {activeTab} URL
                </Label>
                <div className="relative mt-1">
                  <Input
                    id={`${activeTab}-url`}
                    type="url"
                    placeholder={`Enter ${activeTab} URL`}
                    value={urlInput}
                    onChange={(e) => setUrlInput(e.target.value)}
                    className="pr-10"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                    onClick={() => handleAddUrl(activeTab)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {filteredAttachments.length > 0 && (
                <div className="mt-6">
                  <div className="mb-3">
                    <h3 className="text-sm font-medium">
                      Uploaded {activeTab}s
                    </h3>
                  </div>
                  <div className="space-y-2">
                    {filteredAttachments.map((attachment) => (
                      <div
                        key={attachment.id}
                        className="flex items-center justify-between p-3 bg-muted/40 rounded-md"
                      >
                        <div className="flex items-center space-x-3">
                          {attachment.type === "url" ? (
                            <ExternalLink className="h-6 w-6 text-purple-500" />
                          ) : (
                            getFileIcon(attachment.type)
                          )}
                          <div>
                            <p className="text-sm font-medium">
                              {attachment.name}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveAttachment(attachment.id)}
                          className="h-8 w-8 p-0"
                        >
                          <XIcon className="h-4 w-4" />
                          <span className="sr-only">Remove file</span>
                        </Button>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 flex justify-end">
                    <Button
                      size="sm"
                      onClick={() => handleSaveAttachments(activeTab)}
                      className="bg-mobio-blue hover:bg-mobio-blue/90"
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        `Save ${activeTab}s`
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>
          )}

          {/* Demo Access Tab */}
          <TabsContent value="demo" className="mt-0 space-y-6">
            <div>
              <Label htmlFor="demo-url" className="text-base font-medium">
                System Demo URL
              </Label>
              <div className="relative mt-1.5">
                <ExternalLink className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="demo-url"
                  type="url"
                  placeholder="https://example.com/demo"
                  value={demoUrl}
                  onChange={(e) => setDemoUrl(e.target.value)}
                  className="pl-9"
                />
              </div>

              <p className="text-xs text-muted-foreground mt-1">
                Link to access a working demo of this success story
              </p>
            </div>

            <div>
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center space-x-4">
                  <Label className="text-base font-medium">
                    Demo Credentials
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="show-credentials"
                      checked={showCredentials}
                      onCheckedChange={setShowCredentials}
                    />
                    <Label
                      htmlFor="show-credentials"
                      className="text-sm font-normal"
                    >
                      Show Credentials
                    </Label>
                  </div>
                </div>
                <Button
                  size="sm"
                  onClick={() => {
                    setIsAddingAccessDetail(true);
                    handleAccessDetailChange("id", "");
                    handleAccessDetailChange("role", "");
                    handleAccessDetailChange("email", "");
                    handleAccessDetailChange("password", "");
                  }}
                  className="bg-mobio-blue hover:bg-mobio-blue/90"
                >
                  Add Credentials
                </Button>
              </div>

              {/* Show credentials table only when toggle is on */}
              {showCredentials && (
                <>
                  {/* Credentials Form */}
                  {isAddingAccessDetail && (
                    <div className="border rounded-md p-4 mb-4 bg-slate-50">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <Label htmlFor="role" className="text-sm">
                            Role/User Type
                          </Label>
                          <Input
                            id="role"
                            placeholder="e.g., Admin, Viewer, Demo User"
                            value={newAccessDetail.role}
                            onChange={(e) =>
                              handleAccessDetailChange("role", e.target.value)
                            }
                          />
                        </div>
                        <div>
                          <Label htmlFor="email" className="text-sm">
                            Email/Username
                          </Label>
                          <Input
                            id="email"
                            placeholder="Login email or username"
                            value={newAccessDetail.email}
                            onChange={(e) =>
                              handleAccessDetailChange("email", e.target.value)
                            }
                          />
                        </div>
                        <div>
                          <Label htmlFor="password" className="text-sm">
                            Password
                          </Label>
                          <Input
                            id="password"
                            type="text"
                            placeholder="Password"
                            value={newAccessDetail.password}
                            onChange={(e) =>
                              handleAccessDetailChange(
                                "password",
                                e.target.value
                              )
                            }
                          />
                        </div>
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsAddingAccessDetail(false)}
                        >
                          Cancel
                        </Button>
                        <Button size="sm" onClick={handleSaveAccessDetail}>
                          {newAccessDetail.id ? "Update" : "Add"} Credentials
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Credentials Table */}
                  {accessDetails.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Role</TableHead>
                          <TableHead>Email/Username</TableHead>
                          <TableHead>Password</TableHead>
                          <TableHead className="w-[100px]">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {accessDetails.map((detail) => (
                          <TableRow key={detail.id}>
                            <TableCell>{detail.role}</TableCell>
                            <TableCell>{detail.email}</TableCell>
                            <TableCell>{detail.password}</TableCell>
                            <TableCell>
                              <div className="flex gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  onClick={() => handleEditAccessDetail(detail)}
                                >
                                  <Pencil className="h-4 w-4" />{" "}
                                  {/* Use the new icon here */}
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  onClick={() =>
                                    handleRemoveAccessDetail(detail.id)
                                  }
                                >
                                  <XIcon className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground text-sm">
                      No credentials added yet
                    </div>
                  )}
                  {demoUrl && (
                    <div className="mt-4 flex justify-end">
                      <Button
                        size="sm"
                        onClick={handleSaveDemo}
                        className="bg-mobio-blue hover:bg-mobio-blue/90"
                        disabled={isSaving}
                      >
                        {isSaving ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          "Save Demo Details"
                        )}
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
