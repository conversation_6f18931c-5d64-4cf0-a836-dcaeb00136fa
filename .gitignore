# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
package-lock.json

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local
.env
.env.development
.env.test
.env.production

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.idea
.vscode
*.swp
*.swo

# OS generated files
Thumbs.db
.DS_Store
*.log

# Build artifacts
dist/
.cache/

# PWA files
public/sw.js
public/workbox-*.js

# Temporary files
*.temp
*.tmp
.eslintcache

# Package managers
npm-debug.log
yarn-error.log
yarn-debug.log
.pnpm-debug.log

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache