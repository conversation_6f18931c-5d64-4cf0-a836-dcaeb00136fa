
import { Outlet } from "react-router-dom";
import AppSidebar from "./AppSidebar";
import Header from "./Header";
import { Suspense } from "react";
import { Loader2 } from "lucide-react";

const Layout = () => {
  return (
    <div className="min-h-screen w-full flex bg-background overflow-hidden">
      <AppSidebar />
      <div className="flex flex-col flex-1 w-full">
        <Header />
        <main className="flex-1 p-3 sm:p-4 md:p-6 overflow-auto">
          <Suspense fallback={
            <div className="flex h-[80vh] items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-mobio-blue" />
            </div>
          }>
            <div className="animate-fade-in">
              <Outlet />
            </div>
          </Suspense>
        </main>
      </div>
    </div>
  );
};

export default Layout;
