import express from "express";
import { authenticateToken } from "../middleware/auth.js";
import storyController from "../controller/story.controller.js";
import { log, addRequestContext } from "../utils/logger.js";

const router = express.Router();

// Get all stories with pagination and filters
router.get(
  "/",
  authenticateToken,
  (req, res, next) => {
    // Parse array parameters if they exist
    try {
      const arrayParams = [
        "industries",
        "regions",
        "technologies",
        "outcomes",
        "general_tags",
      ];

      for (const param of arrayParams) {
        if (req.query[param]) {
          req.query[param] = JSON.parse(req.query[param]);
        }
      }
      // Title is passed as a regular string, no parsing needed
      next();
    } catch (error) {
      log.error("Failed to parse query parameters", {
        error,
        request: addRequestContext(req),
      });
      res.status(400).json({
        message:
          "Invalid filter format. Arrays must be valid JSON strings. Please ensure array parameters are correctly JSON stringified.",
        status: 400,
      });
    }
  },
  storyController.getStories
);

// Get a specific story by ID
router.get("/:id", authenticateToken, storyController.getStoryById);

// Create a new success story
router.post("/", authenticateToken, storyController.createStory);

// Update an existing success story
router.put("/:id", authenticateToken, storyController.updateStory);

// Delete a success story
router.delete("/:id", authenticateToken, storyController.deleteStory);

export default router;
