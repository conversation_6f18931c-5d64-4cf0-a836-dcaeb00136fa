import { useState, useEffect } from "react";
import { useGroupedTags } from "@/hooks/useGroupedTags";
import { useTagsManagement } from "@/hooks/useTags";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash,
  Loader2,
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import TagDialog from "@/components/AddNewItemDialog";
interface Tag {
  id: string;
  name: string;
  status: boolean;
  usedIn?: number; // Add usedIn property
  type: string; // Add this line
}
interface TagGroup {
  type: string;
  tags: Tag[];
}
const MasterDataTable = ({
  data,
  category,
  onRefresh,
  allCategories,
}: {
  data: Tag[];
  category: string;
  onRefresh: () => void;
  allCategories: string[];
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState(data);
  const [itemName, setItemName] = useState("");
  const [itemType, setItemType] = useState(
    category.toLowerCase().replace(/ /g, "_")
  );
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingItem, setEditingItem] = useState<Tag | null>(null);
  const { createTag, updateTag, deleteTag, isLoading } = useTagsManagement();
  useEffect(() => {
    setFilteredData(data);
  }, [data]);
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);
    setFilteredData(
      data.filter((item) => item.name.toLowerCase().includes(term))
    );
  };
  const handleOpenDialog = (item?: Tag) => {
    setIsDialogOpen(true);
    if (item) {
      setIsEditing(true);
      setEditingItem(item);
      setItemName(item.name);
      // Always use the current tab's type for both add and edit
      setItemType(category.toLowerCase().replace(/ /g, "_"));
    } else {
      setIsEditing(false);
      setEditingItem(null);
      setItemName("");
      setItemType(category.toLowerCase().replace(/ /g, "_"));
    }
  };
  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setIsEditing(false);
    setEditingItem(null);
    setItemName("");
    setItemType(category.toLowerCase().replace(/ /g, "_"));
  };
  const handleSaveItem = async () => {
    if (!itemName.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a name for the item",
        variant: "destructive",
      });
      return;
    }
    try {
      if (isEditing && editingItem) {
        await updateTag(editingItem.id, { name: itemName, type: itemType });
        toast({
          title: "Item Updated",
          description: `Updated "${itemName}" in ${category} category`,
        });
      } else {
        await createTag({
          name: itemName,
          type: itemType,
          status: true,
        });
        toast({
          title: "Item Added",
          description: `Added "${itemName}" to ${category} category`,
        });
      }
      handleCloseDialog();
      onRefresh(); // Refresh the data
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${isEditing ? "update" : "add"} item: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        variant: "destructive",
      });
    }
  };
  const handleStatusToggle = async (id: string, newStatus: boolean) => {
    try {
      await updateTag(id, { status: newStatus });
      toast({
        title: "Status Updated",
        description: `Item status has been ${
          newStatus ? "activated" : "deactivated"
        }`,
      });
      onRefresh(); // Refresh the data
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to update status: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        variant: "destructive",
      });
    }
  };
  const handleDelete = async (id: string) => {
    try {
      await deleteTag(id);
      toast({
        title: "Item Deleted",
        description: `Item has been deleted successfully`,
      });
      onRefresh(); // Refresh the data
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to delete item: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        variant: "destructive",
      });
    }
  };
  return (
    <div className="space-y-4 fade-in">
      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={`Search ${category}...`}
            className="pl-9 max-w-md"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="flex gap-2">
          <Button
            className="bg-mobio-blue hover:bg-mobio-blue/90"
            onClick={() => handleOpenDialog()}
          >
            <Plus className="h-4 w-4 mr-2" /> Add New
          </Button>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Used In</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length > 0 ? (
              filteredData.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.name}</TableCell>
                  <TableCell>
                    <Switch
                      checked={item.status}
                      onCheckedChange={(checked) =>
                        handleStatusToggle(item.id, checked)
                      }
                    />
                  </TableCell>
                  <TableCell>{item.usedIn || 0} stories</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleOpenDialog(item)}
                        >
                          <Edit className="h-4 w-4 mr-2" /> Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDelete(item.id)}>
                          <Trash className="h-4 w-4 mr-2" /> Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={4}
                  className="text-center py-4 text-muted-foreground"
                >
                  No results found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <TagDialog
        open={isDialogOpen}
        onClose={handleCloseDialog}
        itemName={itemName}
        setItemName={setItemName}
        itemType={itemType}
        setItemType={setItemType}
        allCategories={allCategories}
        handleSave={handleSaveItem}
        isEditing={isEditing}
      />
    </div>
  );
};
const MasterData = () => {
  const { data: tagGroups, isLoading, error, refetch } = useGroupedTags();
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-mobio-blue" />
      </div>
    );
  }
  if (error) {
    return (
      <div className="text-center text-red-500">
        <p>Error loading tags: {error.message}</p>
        <Button onClick={refetch} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }
  const allCategories = tagGroups.map(
    (group) =>
      group.type.charAt(0).toUpperCase() +
      group.type.slice(1).replace(/_/g, " ")
  );
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold">Master Data Manager</h1>
      <p className="text-muted-foreground">
        Manage foundational tags and filters for your success stories.
      </p>
      <Tabs defaultValue={tagGroups[0]?.type || "industry"} className="w-full">
        <TabsList className="mb-4 flex overflow-auto">
          {tagGroups.map((group: TagGroup) => (
            <TabsTrigger key={group.type} value={group.type}>
              {group.type.charAt(0).toUpperCase() +
                group.type.slice(1).replace(/_/g, " ")}
            </TabsTrigger>
          ))}
        </TabsList>
        {tagGroups.map((group: TagGroup) => (
          <TabsContent key={group.type} value={group.type}>
            <MasterDataTable
              data={group.tags}
              category={
                group.type.charAt(0).toUpperCase() +
                group.type.slice(1).replace(/_/g, " ")
              }
              onRefresh={refetch}
              allCategories={allCategories}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};
export default MasterData;
