import { useState, useMemo, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FileText, Video, Headphones, Image as ImageIcon } from "lucide-react";
import { usePublishedStories } from "@/hooks/usePublishedStories";
import { useTags } from "@/hooks/useTags";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useGroupedTags } from "@/hooks/useGroupedTags";

interface Story {
  id: string;
  title: string;
  oneLine: string;
  industries: string[];
  regions: string[];
  technologies: string[];
  businessProblem?: string[];  // Make it optional if needed
  has_pdf: boolean;
  has_video: boolean;
  has_audio: boolean;
  has_image: boolean;
}

const StoryCard = ({ story }: { story: Story }) => {
  
  // Memoize the tag arrays to prevent unnecessary re-renders
  const industryTagIds = useMemo(() => story.industries || [], [story.industries]);
  const regionTagIds = useMemo(() => story.regions || [], [story.regions]);
  const technologyTagIds = useMemo(() => story.technologies || [], [story.technologies]);
  const businessProblemTagIds = useMemo(() => story.businessProblem || [], [story.businessProblem]);

  // Use memoized values in the hooks
  const { tags: industryTags } = useTags(industryTagIds);
  const { tags: regionTags } = useTags(regionTagIds);
  const { tags: technologyTags } = useTags(technologyTagIds);
  const { tags: businessProblemTags } = useTags(businessProblemTagIds);

  return (
    <Card className="w-full hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <h3 className="font-medium text-base mb-1">
          <Link to={`/story/${story.id}`} className="hover:text-primary">
            {story.title}
          </Link>
        </h3>
        <p className="text-muted-foreground text-sm mb-3 line-clamp-2">{story.oneLine}</p>
        <div className="flex flex-wrap gap-2 mb-3">
          {industryTags.map((tag) => (
            <Badge key={tag.id} variant="outline" className="bg-mobio-lavender">{tag.name}</Badge>
          ))}
          {regionTags.map((tag) => (
            <Badge key={tag.id} variant="outline" className="bg-mobio-lightblue">{tag.name}</Badge>
          ))}
          {technologyTags.map((tag) => (
            <Badge key={tag.id} variant="outline" className="bg-mobio-gray">{tag.name}</Badge>
          ))}
          {businessProblemTags.map((tag) => (
            <Badge key={tag.id} variant="outline" className="bg-mobio-purple">{tag.name}</Badge>
          ))}
        </div>
        <div className="flex items-center gap-2 text-muted-foreground">
          {story.has_pdf && <FileText className="h-4 w-4" />}
          {story.has_video && <Video className="h-4 w-4" />}
          {story.has_audio && <Headphones className="h-4 w-4" />}
          {story.has_image && <ImageIcon className="h-4 w-4" />}
        </div>
      </CardContent>
    </Card>
  );
};

interface FilterSidebarProps {
  activeFilters: {
    industry: string[];
    technology: string[];
    region: string[];
  };
  setActiveFilters: React.Dispatch<React.SetStateAction<{
    industry: string[];
    technology: string[];
    region: string[];
  }>>;
  tagGroups: any[];
  showMore: boolean;
  setShowMore: React.Dispatch<React.SetStateAction<boolean>>;
}

const FilterSidebar = ({ activeFilters, setActiveFilters, tagGroups, showMore, setShowMore }: FilterSidebarProps) => {
  const renderTags = (type: string) => {
    // Map the filter type to the API type
    const apiType = type === "problem" ? "business_problem" : type;
    const group = tagGroups.find((g: any) => g.type === apiType);
    if (!group) return null;

    const tagsToShow = showMore ? group.tags : group.tags.slice(0, 5);
console.log(tagsToShow);
    return (
      <>
        {tagsToShow.map((tag: any) => (
          <div key={tag.id} className="flex items-center gap-2">
            <input
              type="checkbox"
              id={`filter-${type}-${tag.id}`}
              className="h-4 w-4"
              checked={activeFilters[type].includes(tag.id)} 
              onChange={() => handleFilterChange(type as keyof typeof activeFilters, tag.id)} 
            />
            <label htmlFor={`filter-${type}-${tag.id}`} className="text-sm">{tag.name}</label>
          </div>
        ))}
        {group.tags.length > 5 && (
          <Button variant="link" size="sm" onClick={() => setShowMore(!showMore)}>
            {showMore ? "Show Less" : "Show More"}
          </Button>
        )}
      </>
    );
  };

  const handleFilterChange = (category: keyof typeof activeFilters, value: string) => {
    setActiveFilters((prev: any) => {
      if (prev[category].includes(value)) {
        return {
          ...prev,
          [category]: prev[category].filter((item: string) => item !== value)
        };
      } else {
        return {
          ...prev,
          [category]: [...prev[category], value]
        };
      }
    });
  };

  return (
    <div className="w-full lg:col-span-1">
      <div className="sticky top-20">
        <div className="bg-white p-4 rounded-lg border mb-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-medium">Filters</h3>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setActiveFilters({ industry: [], technology: [], region: [] })}
              className="text-sm h-auto py-1 px-2"
            >
              Clear All
            </Button>
          </div>
          
          <Accordion type="multiple" defaultValue={["industry", "technology", "region"]}>
            <AccordionItem value="industry">
              <AccordionTrigger className="py-2 bg-mobio-lavender rounded-md px-2">Industry</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-1.5 mt-2">
                  {renderTags("industry")}
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="technology">
              <AccordionTrigger className="py-2 bg-mobio-gray rounded-md px-2">Technology</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-1.5 mt-2">
                  {renderTags("technology")}
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="region">
              <AccordionTrigger className="py-2 bg-mobio-lightblue rounded-md px-2">Region</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-1.5 mt-2">
                  {renderTags("region")}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </div>
  );
};

const Home = () => {
  const [searchTitle, setSearchTitle] = useState('');
  const [activeFilters, setActiveFilters] = useState({
    industry: [] as string[],
    technology: [] as string[],
    region: [] as string[]
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [showMore, setShowMore] = useState(false);
  const { data: tagGroups, isLoading: tagsLoading } = useGroupedTags();
  
  // Remove URL-based search handling
  useEffect(() => {
    const handleSearchUpdate = (event: CustomEvent) => {
      setSearchTitle(event.detail.query);
    };

    window.addEventListener('updateSearch', handleSearchUpdate as EventListener);
    return () => {
      window.removeEventListener('updateSearch', handleSearchUpdate as EventListener);
    };
  }, []);

  // Create filters object for the hook
  const filters = useMemo(() => ({
    title: searchTitle || undefined,
    industries: activeFilters.industry.length > 0 ? activeFilters.industry : undefined,
    technologies: activeFilters.technology.length > 0 ? activeFilters.technology : undefined,
    regions: activeFilters.region.length > 0 ? activeFilters.region : undefined
  }), [searchTitle, activeFilters]);

  const { stories, loading: storiesLoading, error } = usePublishedStories(filters);

  // Filter stories based on search title and active filters
  const filteredStories = useMemo(() => {
    if (!stories) return [];
    
    return stories.filter(story => {
      // Title search filter
      const matchesTitle = !searchTitle || 
        story.title.toLowerCase().includes(searchTitle.toLowerCase());
      
      // Other filters
      const matchesIndustry = activeFilters.industry.length === 0 || 
        story.industries.some(id => activeFilters.industry.includes(id));
      
      const matchesTechnology = activeFilters.technology.length === 0 || 
        story.technologies.some(id => activeFilters.technology.includes(id));
      
      const matchesRegion = activeFilters.region.length === 0 || 
        story.regions.some(id => activeFilters.region.includes(id));

      return matchesTitle && matchesIndustry && matchesTechnology && matchesRegion;
    });
  }, [stories, searchTitle, activeFilters]);

  if (storiesLoading || tagsLoading) {
    return (
      <div className="container mx-auto pb-16 md:pb-4">
        <div className="lg:grid lg:grid-cols-4 lg:gap-6">
          <div className="lg:col-span-1">
            <div className="sticky top-20">
              <div className="bg-white p-4 rounded-lg border mb-4 animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-4 bg-gray-200 rounded w-3/4"></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div className="lg:col-span-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[...Array(6)].map((_, index) => (
                <Card key={index} className="w-full animate-pulse">
                  <CardContent className="p-4">
                    <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
                    <div className="flex flex-wrap gap-2 mb-3">
                      <div className="h-6 bg-gray-200 rounded w-20"></div>
                      <div className="h-6 bg-gray-200 rounded w-24"></div>
                      <div className="h-6 bg-gray-200 rounded w-16"></div>
                    </div>
                    <div className="flex gap-2">
                      <div className="h-4 w-4 bg-gray-200 rounded"></div>
                      <div className="h-4 w-4 bg-gray-200 rounded"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto pb-16 md:pb-4" data-browse-component>
      {/* Mobile Filter Button */}
      <div className="lg:hidden mb-4">
        <Button 
          onClick={() => setIsFilterOpen(!isFilterOpen)} 
          variant="outline" 
          className="w-full"
        >
          {isFilterOpen ? "Hide Filters" : "Show Filters"}
        </Button>
      </div>

      <div className="lg:grid lg:grid-cols-4 lg:gap-6">
        <div className={`${isFilterOpen ? 'block' : 'hidden'} lg:block`}>
          <FilterSidebar 
            activeFilters={activeFilters} 
            setActiveFilters={setActiveFilters} 
            tagGroups={tagGroups} 
            showMore={showMore} 
            setShowMore={setShowMore} 
          />
        </div>
        <div className="lg:col-span-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredStories.map((story) => (
              <StoryCard key={story.id} story={story} />
            ))}
          </div>
          {filteredStories.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No stories found matching the selected filters
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Home;