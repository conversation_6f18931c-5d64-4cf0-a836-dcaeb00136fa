import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Download, FileText, Link } from "lucide-react"; // Added ExternalLink for DemoMediaDisplay
import DemoMediaDisplay from "./DemoMediaDisplay"; // Import the new component

export type MediaType = "images" | "videos" | "audio" | "documents" | "demo";

interface MediaItem {
  id?: string;
  name?: string;
  url?: string; // Made optional to match source data structure
  previewUrl?: string;
  type?: string; // 'url' or other to determine download/link
  credentials?: Array<{ role: string; email: string; password: string }>; // For demo type
}

interface StoryMedia {
  images?: MediaItem[];
  videos?: MediaItem[];
  audio?: MediaItem[];
  documents?: MediaItem[];
  demo?: MediaItem[];
}

interface StoryMediaSectionProps {
  media: StoryMedia | undefined;
  activeMediaTab: MediaType;
  onActiveMediaTabChange: (value: MediaType) => void;
  onDownloadItem: (item: MediaItem) => void;
}

const StoryMediaSection = ({
  media,
  activeMediaTab,
  onActiveMediaTabChange,
  onDownloadItem,
}: StoryMediaSectionProps) => {
  const getMediaByType = (type: MediaType): MediaItem[] => {
    if (!media) return [];
    switch (type) {
      case "images":
        return media.images || [];
      case "videos":
        return media.videos || [];
      case "audio":
        return media.audio || [];
      case "documents":
        return media.documents || [];
      case "demo":
        return media.demo || [];
      default:
        return [];
    }
  };

  const getMediaCount = (type: MediaType): number => {
    return getMediaByType(type).length;
  };

  const activeMedia = getMediaByType(activeMediaTab);

  return (
    <Card className="overflow-hidden">
      <div className="bg-slate-50 p-4 border-b">
        <h2 className="text-xl font-medium text-mobio-blue">Story Media</h2>
      </div>

      <Tabs
        value={activeMediaTab}
        onValueChange={(value) => onActiveMediaTabChange(value as MediaType)}
      >
        <div className="border-b">
          <TabsList className="bg-transparent p-0 h-12">
            <TabsTrigger
              value="images"
              className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-b-mobio-blue data-[state=active]:shadow-none rounded-none px-6 h-full"
            >
              Images ({getMediaCount("images")})
            </TabsTrigger>
            <TabsTrigger
              value="videos"
              className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-b-mobio-blue data-[state=active]:shadow-none rounded-none px-6 h-full"
            >
              Videos ({getMediaCount("videos")})
            </TabsTrigger>
            <TabsTrigger
              value="audio"
              className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-b-mobio-blue data-[state=active]:shadow-none rounded-none px-6 h-full"
            >
              Audio ({getMediaCount("audio")})
            </TabsTrigger>
            <TabsTrigger
              value="documents"
              className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-b-mobio-blue data-[state=active]:shadow-none rounded-none px-6 h-full"
            >
              Documents ({getMediaCount("documents")})
            </TabsTrigger>
            <TabsTrigger
              value="demo"
              className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-b-mobio-blue data-[state=active]:shadow-none rounded-none px-6 h-full"
            >
              Demo ({getMediaCount("demo")})
            </TabsTrigger>
          </TabsList>
        </div>

        {activeMediaTab !== "demo" && (
          <CardContent className="p-6">
            {activeMedia.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {activeMedia.map((item) => (
                  <div
                    key={item.id ?? item.url} // Use item.url as fallback key
                    className="group relative border rounded-lg overflow-hidden hover:shadow-md transition-all"
                  >
                    {activeMediaTab === "images" ? (
                      <img
                        src={
                          item.id
                            ? `https://drive.google.com/thumbnail?id=${item.id}`
                            : item.previewUrl || item.url // Fallback to item.url if previewUrl is not available
                        }
                        alt={item.name ?? "Image"}
                        className="w-full h-48 object-cover"
                        referrerPolicy="no-referrer"
                      />
                    ) : (
                      <div className="w-full h-48 bg-slate-100 flex items-center justify-center">
                        {activeMediaTab === "videos" && item.previewUrl && (
                          <iframe
                            src={item.previewUrl}
                            title={item.name ?? "Video"}
                            className="w-full h-full"
                            allowFullScreen
                          ></iframe>
                        )}
                        {activeMediaTab === "audio" && item.previewUrl && (
                          <iframe
                            src={item.previewUrl}
                            title={item.name ?? "Audio"}
                            className="w-full h-full"
                          ></iframe>
                        )}
                        {activeMediaTab === "documents" && (
                          <div className="text-center">
                            <div className="inline-flex h-16 w-16 items-center justify-center">
                              <FileText className="h-16 w-16 text-amber-500" />
                            </div>
                            <p className="mt-2 text-sm font-medium text-slate-600">
                              {item.name ?? "Document"}
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-end">
                      <div className="w-full p-3 text-white">
                        <div className="flex items-center justify-between">
                          <div className="truncate">
                            <p className="font-medium text-sm">
                              {item.name ?? item.url}
                            </p>
                          </div>
                          {item.type === "url" ? (
                            <Button
                              variant="secondary"
                              size="icon"
                              className="h-8 w-8 flex-shrink-0"
                              onClick={() => window.open(item.url, "_blank")}
                            >
                              <Link className="h-4 w-4" />
                            </Button>
                          ) : (
                            <Button
                              variant="secondary"
                              size="icon"
                              className="h-8 w-8 flex-shrink-0"
                              onClick={() => onDownloadItem(item)}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex h-32 items-center justify-center border border-dashed rounded-lg">
                <p className="text-muted-foreground text-center">
                  No {activeMediaTab} available for this success story
                </p>
              </div>
            )}
          </CardContent>
        )}
        {activeMediaTab === "demo" && (
          <DemoMediaDisplay
            demoMediaItems={activeMedia
              .filter((item): item is MediaItem & { url: string } => typeof item.url === 'string')
            }
          />
        )}
      </Tabs>
    </Card>
  );
};

export default StoryMediaSection;
