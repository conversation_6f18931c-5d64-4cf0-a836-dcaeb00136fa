import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
} from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Define the user types
export type UserRole = "admin" | "user" | null;

interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (
    name: string,
    email: string,
    password: string,
    role: string
  ) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const API_BASE_URL = import.meta.env.VITE_API_URL;

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem("winwise_token");
    const savedUser = localStorage.getItem("winwise_user");
    if (token && savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (e) {
        // Log the error to address SonarQube S2486 and aid debugging
        if (e instanceof Error) {
          console.error(
            "AuthContext: Failed to parse user from localStorage. Error:",
            e.message,
            e.stack
          );
        } else {
          console.error(
            "AuthContext: Failed to parse user from localStorage with unknown error:",
            e
          );
        }
        localStorage.removeItem("winwise_user");
        localStorage.removeItem("winwise_token");
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      const requestBody = { email, password };

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        toast.error(data.message ?? "Login failed");

        setIsLoading(false);
        return false;
      }

      const userData: User = data.data.user;
      const token = data.data.session.access_token;

      setUser(userData);
      localStorage.setItem("winwise_user", JSON.stringify(userData));
      localStorage.setItem("winwise_token", token);

      toast.success(`Welcome back, ${userData.name}!`);

      if (userData.role === "admin") {
        navigate("/admin");
      } else {
        navigate("/");
      }

      setIsLoading(false);
      return true;
    } catch (error) {
      console.error("Login error:", error);
      toast.error("An unexpected error occurred during login.");

      // Log curl command in case of network or unknown errors
      console.info("Try this cURL command in terminal:");
      console.info(`curl -X POST ${API_BASE_URL}/api/auth/login \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify({ email, password }, null, 2)}'`);

      setIsLoading(false);
      return false;
    }
  };

  const register = async (
    name: string,
    email: string,
    password: string,
    role: string
  ): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, email, password, role }),
      });

      const data = await response.json();

      if (!response.ok) {
        toast.error(data.message ?? "Registration failed");
        setIsLoading(false);
        return false;
      }

      toast.success("Registration successful! Please log in.");
      setIsLoading(false);
      navigate("/login"); // Redirect to login after successful registration
      return true;
    } catch (error) {
      console.error("Registration error:", error);
      toast.error("An unexpected error occurred during registration.");
      setIsLoading(false);
      return false;
    }
  };

  const logout = async () => {
    const token = localStorage.getItem("winwise_token");

    // Client-side cleanup first to ensure UI reflects logout state immediately
    setUser(null);
    localStorage.removeItem("winwise_user");
    localStorage.removeItem("winwise_token");
    navigate("/login");
    toast.info("You've been logged out");

    if (token) {
      try {
        const response = await fetch(`${API_BASE_URL}/api/auth/logout`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.error(
            "Server-side logout failed:",
            response.status,
            errorData.message ?? "Unknown server error"
          );
        }
      } catch (error) {
        console.error("Error during server-side logout:", error);
      }
    }
  };

  const contextValue = useMemo(
    () => ({
      user,
      isAuthenticated: !!user,
      isAdmin: user?.role === "admin",
      login,
      register,
      logout,
      isLoading,
    }),
    [user, isLoading, login, register, logout]
  );

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
