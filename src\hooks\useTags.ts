import { useState, useEffect } from "react";
import axios from "axios"; // Keep for AxiosError type checking
import { apiClient } from "./useApi"; // Import the configured apiClient

interface Tag {
  id: string;
  name: string;
  type: string;
  status?: boolean;
}

export const useTags = (ids: string[]) => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!ids.length) {
      setTags([]);
      setLoading(false);
      setError(null);
      return;
    }

    const fetchTags = async () => {
      setLoading(true);
      setError(null);
      try {
        // Use the apiClient instance
        const response = await apiClient.post(`/api/tags/by-ids`, { ids }); // URL is relative
        const data = response.data;
        if (data.success) {
          setTags(data.data ?? []);
        } else {
          throw new Error(data.message ?? "Failed to fetch tags");
        }
      } catch (err) {
        console.error("Error fetching tags:", err);
        if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data?.message || err.message);
        } else if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("An unknown error occurred while fetching tags");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchTags();
  }, [ids]);

  return { tags, loading, error };
};

export const useTagsManagement = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createTag = async (tag: { name: string; type: string; status?: boolean }) => {
    setIsLoading(true);
    setError(null);
    try {
      // Use the apiClient instance
      const response = await apiClient.post(`/api/tags`, tag); // URL is relative
      return response.data;
    } catch (err) {
      console.error("Error creating tag:", err);
      if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.message || "Failed to create tag");
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("An unknown error occurred while creating tag");
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateTag = async (id: string, tag: { name?: string; status?: boolean; type?: string }) => {
    setIsLoading(true);
    setError(null);
    try {
      // Use the apiClient instance
      const response = await apiClient.put(`/api/tags/${id}`, tag); // URL is relative
      return response.data;
    } catch (err) {
      console.error("Error updating tag:", err);
      if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.message || "Failed to update tag");
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("An unknown error occurred while updating tag");
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteTag = async (id: string) => {
    setIsLoading(true);
    setError(null);
    try {
      // Use the apiClient instance
      const response = await apiClient.delete(`/api/tags/${id}`); // URL is relative
      return response.data;
    } catch (err) {
      console.error("Error deleting tag:", err);
      if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data?.message || "Failed to delete tag");
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("An unknown error occurred while deleting tag");
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    createTag,
    updateTag,
    deleteTag,
    isLoading,
    error,
  };
};
