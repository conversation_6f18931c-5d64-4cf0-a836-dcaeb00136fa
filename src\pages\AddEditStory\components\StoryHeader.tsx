
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface StoryHeaderProps {
  isEditMode: boolean;
  isSaving: boolean;
  onSave: () => void;
  onPublish: () => void;
}

export const StoryHeader = ({ isEditMode, isSaving, onSave, onPublish }: StoryHeaderProps) => {
  return (
    <div className="flex justify-between items-center">
      <h1 className="text-2xl font-semibold">
        {isEditMode ? "Edit Success Story" : "Add New Success Story"}
      </h1>
      <div className="flex space-x-2">
        <Button 
          variant="outline" 
          disabled={isSaving}
          onClick={onSave}
        >
          {isSaving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
          Save as Draft
        </Button>
        <Button 
          disabled={isSaving}
          onClick={onPublish}
        >
          {isSaving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
          Publish Story
        </Button>
      </div>
    </div>
  );
};
