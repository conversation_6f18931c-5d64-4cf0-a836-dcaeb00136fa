import { useGroupedTags } from "./useGroupedTags";

export const useTagNames = () => {
  const { data: tagGroups } = useGroupedTags();

  const getTagNames = (tagIds: string[]) => {
    if (!tagGroups) return [];

    return tagIds.map((id) => {
      const tag = tagGroups.flatMap((group) => group.tags).find((tag) => tag.id === id);
      return tag ? tag.name : id;
    });
  };

  return { getTagNames };
};