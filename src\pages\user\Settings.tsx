
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  Sun,
  Moon,
  Bell,
  MessageSquare,
  Download,
  HelpCircle
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const Settings = () => {
  const [theme, setTheme] = useState<"light" | "dark" | "system">("light");
  const [notifications, setNotifications] = useState({
    newStories: true,
    suggestions: false,
    updates: true
  });
  const [feedbackText, setFeedbackText] = useState("");

  const handleThemeChange = (newTheme: "light" | "dark" | "system") => {
    setTheme(newTheme);
    toast({
      title: "Theme updated",
      description: `Theme set to ${newTheme}`,
    });
  };

  const handleNotificationChange = (key: keyof typeof notifications) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleSubmitFeedback = () => {
    if (feedbackText.trim()) {
      toast({
        title: "Feedback submitted",
        description: "Thank you for your feedback!",
      });
      setFeedbackText("");
    } else {
      toast({
        title: "Empty feedback",
        description: "Please enter some feedback before submitting",
        variant: "destructive",
      });
    }
  };

  const handleDataExport = () => {
    toast({
      title: "Exporting data",
      description: "Your data is being prepared for export",
    });
  };

  return (
    <div className="container max-w-3xl mx-auto space-y-6 pb-16 md:pb-4">
      <h1 className="text-2xl font-bold">Settings</h1>
      
      <Tabs defaultValue="preferences" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="feedback">Feedback</TabsTrigger>
          <TabsTrigger value="help">Help</TabsTrigger>
        </TabsList>
        
        <TabsContent value="preferences">
          <div className="space-y-4 mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Appearance</CardTitle>
                <CardDescription>
                  Customize how WinWise looks on your device
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <Button 
                    variant={theme === "light" ? "default" : "outline"} 
                    className="flex flex-col items-center justify-center h-auto py-4"
                    onClick={() => handleThemeChange("light")}
                  >
                    <Sun className="h-5 w-5 mb-2" />
                    <span>Light</span>
                  </Button>
                  <Button 
                    variant={theme === "dark" ? "default" : "outline"} 
                    className="flex flex-col items-center justify-center h-auto py-4"
                    onClick={() => handleThemeChange("dark")}
                  >
                    <Moon className="h-5 w-5 mb-2" />
                    <span>Dark</span>
                  </Button>
                  <Button 
                    variant={theme === "system" ? "default" : "outline"} 
                    className="flex flex-col items-center justify-center h-auto py-4"
                    onClick={() => handleThemeChange("system")}
                  >
                    <div className="flex mb-2">
                      <Sun className="h-5 w-5" />
                      <Moon className="h-5 w-5" />
                    </div>
                    <span>System</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Notifications</CardTitle>
                <CardDescription>
                  Choose what notifications you receive
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="new-stories">New Success Stories</Label>
                    <p className="text-sm text-muted-foreground">
                      Be notified when new stories are added in your areas of interest
                    </p>
                  </div>
                  <Switch 
                    id="new-stories" 
                    checked={notifications.newStories}
                    onCheckedChange={() => handleNotificationChange("newStories")}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="suggestions">Story Suggestions</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive suggestions for stories based on your browsing history
                    </p>
                  </div>
                  <Switch 
                    id="suggestions" 
                    checked={notifications.suggestions}
                    onCheckedChange={() => handleNotificationChange("suggestions")}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="updates">Platform Updates</Label>
                    <p className="text-sm text-muted-foreground">
                      Get notified about new features and improvements
                    </p>
                  </div>
                  <Switch 
                    id="updates" 
                    checked={notifications.updates}
                    onCheckedChange={() => handleNotificationChange("updates")}
                  />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Data</CardTitle>
                <CardDescription>
                  Manage your data within WinWise
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-1">Export Your Data</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Download all your saved stories and browsing history
                    </p>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex items-center gap-1"
                      onClick={handleDataExport}
                    >
                      <Download className="h-4 w-4" /> Export Data
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="feedback">
          <Card className="mt-4">
            <CardHeader>
              <CardTitle>Send Feedback</CardTitle>
              <CardDescription>
                Help us improve WinWise by sharing your thoughts and suggestions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea 
                placeholder="Tell us what you think about WinWise..."
                className="min-h-[150px]"
                value={feedbackText}
                onChange={(e) => setFeedbackText(e.target.value)}
              />
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setFeedbackText("")}>Clear</Button>
              <Button onClick={handleSubmitFeedback}>Submit Feedback</Button>
            </CardFooter>
          </Card>
          
          <Card className="mt-4">
            <CardHeader>
              <CardTitle>Contact Support</CardTitle>
              <CardDescription>
                Get help with any issues you're experiencing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-muted-foreground" />
                <span><EMAIL></span>
              </div>
              <Button variant="outline" className="w-full" asChild>
                <a href="mailto:<EMAIL>">Email Support</a>
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="help">
          <Card className="mt-4">
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>
                Common questions about using WinWise
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                  <AccordionTrigger>
                    How do I use voice search?
                  </AccordionTrigger>
                  <AccordionContent>
                    Click the microphone icon in the search bar and speak your query clearly.
                    For example, try saying "Show me AI success stories in Healthcare" or
                    "Find Retail case studies in Europe".
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-2">
                  <AccordionTrigger>
                    How do I save a success story?
                  </AccordionTrigger>
                  <AccordionContent>
                    When viewing a success story, click the "Save" button at the top of the page.
                    Saved stories will appear in your Library under the "Saved Stories" tab.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-3">
                  <AccordionTrigger>
                    How can I filter search results?
                  </AccordionTrigger>
                  <AccordionContent>
                    Use the filters panel on the left side of the search results page to filter
                    by industry, technology, region, and business problem. On mobile devices,
                    tap the "Filters" button to access filtering options.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-4">
                  <AccordionTrigger>
                    Can I download success stories?
                  </AccordionTrigger>
                  <AccordionContent>
                    Yes, you can download any media associated with a success story by viewing
                    the story and clicking the "Download" button in the media section. You can
                    also download all your saved stories from the Library page.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card>
          
          <Card className="mt-4">
            <CardHeader>
              <CardTitle>Useful Resources</CardTitle>
              <CardDescription>
                Additional resources to help you get the most out of WinWise
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5 text-muted-foreground" />
                <a href="#" className="text-primary hover:underline">User Guide</a>
              </div>
              <div className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5 text-muted-foreground" />
                <a href="#" className="text-primary hover:underline">Video Tutorials</a>
              </div>
              <div className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5 text-muted-foreground" />
                <a href="#" className="text-primary hover:underline">Best Practices</a>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
