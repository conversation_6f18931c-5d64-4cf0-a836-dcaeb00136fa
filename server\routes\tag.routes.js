import express from "express";
import tagController from "../controller/tag.controller.js";
import { authenticateToken } from "../middleware/auth.js";

const router = express.Router();

// Get tags by type (industry, region, technology, etc.)
router.get("/type/:type", authenticateToken,tagController.getTagsByType);

// Get all tags grouped by type
router.get("/grouped", authenticateToken, tagController.getAllTagsGrouped);

router.post("/by-ids", authenticateToken,tagController.getTagsByIds);

// Get tag by ID
router.get("/:id",authenticateToken, tagController.getTagById);

// Other CRUD routes
router.post("/",authenticateToken, tagController.createTag);
router.put("/:id",authenticateToken, tagController.updateTag);
router.delete("/:id", authenticateToken,tagController.deleteTag);

export default router;
