
import { Link, useLocation } from "react-router-dom";
import { Home, Search, BookMarked, Settings } from "lucide-react";

const UserFooter = () => {
  const location = useLocation();
  const currentPath = location.pathname;
  
  const isActive = (path: string) => {
    if (path === '/') {
      return currentPath === path;
    }
    return currentPath.startsWith(path);
  };

  return (
    <footer className="md:hidden fixed bottom-0 left-0 right-0 border-t bg-background z-10 shadow-lg">
      <div className="flex justify-around py-2">
        <Link 
          to="/"
          className={`flex flex-col items-center justify-center p-2 transition-all duration-200 ${
            isActive('/') 
              ? "text-primary" 
              : "text-muted-foreground hover:text-primary"
          }`}
        >
          <Home className={`h-5 w-5 ${isActive('/') ? 'animate-pulse-soft' : ''}`} />
          <span className={`text-xs mt-1 ${isActive('/') ? 'font-medium' : ''}`}>Home</span>
        </Link>
        <Link 
          to="/browse"
          className={`flex flex-col items-center justify-center p-2 transition-all duration-200 ${
            isActive('/browse') 
              ? "text-primary" 
              : "text-muted-foreground hover:text-primary"
          }`}
        >
          <Search className={`h-5 w-5 ${isActive('/browse') ? 'animate-pulse-soft' : ''}`} />
          <span className={`text-xs mt-1 ${isActive('/browse') ? 'font-medium' : ''}`}>Browse</span>
        </Link>
        <Link 
          to="/library"
          className={`flex flex-col items-center justify-center p-2 transition-all duration-200 ${
            isActive('/library') 
              ? "text-primary" 
              : "text-muted-foreground hover:text-primary"
          }`}
        >
          <BookMarked className={`h-5 w-5 ${isActive('/library') ? 'animate-pulse-soft' : ''}`} />
          <span className={`text-xs mt-1 ${isActive('/library') ? 'font-medium' : ''}`}>Library</span>
        </Link>
        <Link 
          to="/settings"
          className={`flex flex-col items-center justify-center p-2 transition-all duration-200 ${
            isActive('/settings') 
              ? "text-primary" 
              : "text-muted-foreground hover:text-primary"
          }`}
        >
          <Settings className={`h-5 w-5 ${isActive('/settings') ? 'animate-pulse-soft' : ''}`} />
          <span className={`text-xs mt-1 ${isActive('/settings') ? 'font-medium' : ''}`}>Settings</span>
        </Link>
      </div>
    </footer>
  );
};

export default UserFooter;
