import React, { createContext, useContext, useState, useEffect } from "react";
import { toast } from "sonner";
import { nanoid } from "nanoid";
import { useStoryDetails } from "@/hooks/useStoryDetails";
import {
  Story,
  Attachment,
  Credential,
  AiState,
  SuggestedTag,
  FileState,
} from "./types";
// Initial States
const INITIAL_STORY_STATE: Story = {
  id: "",
  title: "",
  customer: "",
  clientSegment: "",
  oneLine: "", // Add this line
  businessChallenges: "",
  summary: "",
  successStory: "", // Add this line
  publishDate: new Date(),
  status: "draft",
  confidential: false,
  categories: {
    industry: [],
    technology: [],
    businessProblem: [],
    region: [],
    outcome: [],
    general_tag: [], 
  },
  context: {
    background: "",
    challenge: "",
    solution: "",
    benefits: "",
    results: "",
    quote: "",
    quoteAuthor: "",
    quotePosition: "",
  },
  attachments: [],
  credentials: [],
  createdBy: "",
  createdAt: new Date(),
  updatedAt: new Date(),
  demoLink: "",
  demoAccessDetails: "",
  showDemoToEndUsers: false,
  roleVisibility: {
    cxo: false,
    sales: false,
    presales: false,
    marketing: false,
    delivery: false,
  },
};
const INITIAL_ACCESS_DETAIL: Credential = {
  id: "",
  role: "",
  email: "",
  password: "",
};
const INITIAL_AI_STATE: AiState = {
  source: "pdf",
  sourceUrl: "",
  isProcessing: false,
  processed: false,
};
type DocumentResponse = any;
// Context Type
interface StoryContextType {
  storyData: Story;
  isLoading: boolean;
  isSaving: boolean;
  isEditMode: boolean;
  handleInputChange: (field: keyof Story, value: any) => void;
  handleContextChange: (field: keyof Story["context"], value: string) => void;
  handleCategoryChange: (
    categoryType: keyof Story["categories"],
    values: string[]
  ) => void;
  handleRoleVisibilityChange: (role: string, value: boolean) => void;
  handleAddAttachment: (attachment: Attachment) => void;
  handleRemoveAttachment: (attachmentId: string) => void;
  saveStory: (status?: "draft" | "published" | "archived") => void;
  accessDetails: Credential[];
  newAccessDetail: Credential;
  isAddingAccessDetail: boolean;
  handleAccessDetailChange: (field: keyof Credential, value: string) => void;
  handleSaveAccessDetail: () => void;
  handleEditAccessDetail: (detail: Credential) => void;
  handleRemoveAccessDetail: (detailId: string) => void;
  setIsAddingAccessDetail: React.Dispatch<React.SetStateAction<boolean>>;
  aiPrompt: string;
  setAiPrompt: React.Dispatch<React.SetStateAction<string>>;
  isAiPanelOpen: boolean;
  setIsAiPanelOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleAiContentGenerate: () => void;
  isProcessingAi: boolean;
  aiState: AiState;
  files: FileState;
  setAiSource: (source: "pdf" | "url" | "text") => void;
  setSourceUrl: (url: string) => void;
  handleAiGenerate: () => void;
  handleFileUpload: (
    type: keyof FileState,
    e: React.ChangeEvent<HTMLInputElement>
  ) => void;
  resetFiles: (type: keyof FileState) => void;
  aiDescription: string;
  setAiDescription: React.Dispatch<React.SetStateAction<string>>;
  suggestedTags: SuggestedTag[];
  handleAddSuggestedTag: (tagId: string) => void;
  documentResponse: any;
  setDocumentResponse: React.Dispatch<React.SetStateAction<any>>;
  storyResponse: any; // Add new type for story response
  setStoryResponse: React.Dispatch<React.SetStateAction<any>>; // Add new setter
  publishedPayload: any; // Add new type
  setPublishedPayload: React.Dispatch<React.SetStateAction<any>>; // Add new setter
}
// Context
const StoryContext = createContext<StoryContextType | undefined>(undefined);
// Hook
export const useStory = (): StoryContextType => {
  const context = useContext(StoryContext);
  if (!context) throw new Error("useStory must be used within a StoryProvider");
  return context;
};
// Provider
export const StoryProvider: React.FC<{
  children: React.ReactNode;
  editId?: string;
}> = ({ children, editId }) => {
  const isEditMode = Boolean(editId);
  const { story, isLoading: isStoryLoading, error } = useStoryDetails(editId || '');
  const [storyData, setStoryData] = useState<Story>(INITIAL_STORY_STATE);
  const [isLoading, setIsLoading] = useState(false);  // Changed initial state to false
  const [isSaving, setIsSaving] = useState(false);
  const [aiPrompt, setAiPrompt] = useState("");
  const [isAiPanelOpen, setIsAiPanelOpen] = useState(false);
  const [isProcessingAi, setIsProcessingAi] = useState(false);
  const [aiState, setAiState] = useState<AiState>(INITIAL_AI_STATE);
  const [aiDescription, setAiDescription] = useState("");
  const [documentResponse, setDocumentResponse] =
    useState<DocumentResponse | null>(null);
  const [storyResponse, setStoryResponse] = useState<any>(null); // Add new state
  const [accessDetails, setAccessDetails] = useState<Credential[]>([]);
  const [newAccessDetail, setNewAccessDetail] = useState<Credential>(
    INITIAL_ACCESS_DETAIL
  );
  const [isAddingAccessDetail, setIsAddingAccessDetail] = useState(false);
  const [files, setFiles] = useState<FileState>({
    pdf: null,
    image: null,
    document: null,
  });
  const [suggestedTags, setSuggestedTags] = useState<SuggestedTag[]>([
    { id: "1", name: "Cost Reduction", category: "Business Problem" },
    { id: "2", name: "Healthcare", category: "Industry" },
    { id: "3", name: "Machine Learning", category: "Technology" },
  ]);
  // Edit mode data loader
  useEffect(() => {
    let mounted = true;
    const loadStoryData = async () => {
      if (!isEditMode || !editId) {
        setStoryData(INITIAL_STORY_STATE); // Reset to initial if not in edit mode
        setIsLoading(false);
        return;
      }
      
      //isLoading refers to the StoryContext's loading state,
      // isStoryLoading refers to useStoryDetails's loading state.
      if (isStoryLoading) {
        setIsLoading(true); // Keep context loading true while details hook is loading
        return; // Wait for useStoryDetails to finish
      }

      setIsLoading(true); // Context is now actively trying to process data

      try {
        if (error) { // Check for error from useStoryDetails
          console.error('Error from useStoryDetails:', error);
          throw new Error(`Failed to fetch story details from API: ${error}`);
        }

        if (!story) { // If no error, but story is still null after loading, then it's an issue.
          console.error('useStoryDetails finished loading but story is null. editId:', editId);
          throw new Error('No data received from API after attempting to load details.');
        }
        
        // Proceed with transforming the story
        const validStatus = (status: string): "draft" | "published" | "archived" => {
          if (status === "draft" || status === "published" || status === "archived") {
            return status;
          }
          return "draft"; // default fallback
        };
        
        // Then in the transformedStory:
        const transformedStory: Story = {
          ...INITIAL_STORY_STATE,
          id: story.id || '',
          title: story.title || '',
          customer: story.customer || '',
          clientSegment: story.clientSegment || '',
          oneLine: story.oneLineSummary || '',
          businessChallenges: story.businessChallenges || '',
          summary: story.summary || '',
          successStory: story.successStoryDetail || '',
          status: validStatus(story.status || 'draft'),
          confidential: false,
          createdBy: story.created_by || '',
          createdAt: story.created ? new Date(story.created) : new Date(),
          updatedAt: story.updated ? new Date(story.updated) : new Date(),
          categories: {
            industry: Array.isArray(story.categories?.industry) ? story.categories.industry : [],
            technology: Array.isArray(story.categories?.technology) ? story.categories.technology : [],
            businessProblem: Array.isArray(story.categories?.businessProblem) ? story.categories.businessProblem : [],
            region: Array.isArray(story.categories?.region) ? story.categories.region : [],
            outcome: Array.isArray(story.categories?.outcome) ? story.categories.outcome : [],
            general_tag:  []
          },
          attachments: [
            ...(story.media?.images || []).map(asset => ({
              id: asset.id || '',
              name: asset.name || '',
              type: 'image',
              url: asset.url || '',
              previewUrl: asset.previewUrl || ''
            })),
            ...(story.media?.documents || []).map(asset => ({
              id: asset.id || '',
              name: asset.name || '',
              type: 'document',
              url: asset.url || '',
              previewUrl: asset.previewUrl || ''
            }))
          ]
        };
        
        setStoryData(transformedStory);
      } catch (error) {
        if (!mounted) return;
        console.error('Error fetching story:', error);
        toast.error('Failed to load story data');
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };
    loadStoryData();
    return () => {
      mounted = false;
    };
  }, [isEditMode, editId, story, isStoryLoading, error]); // Added isStoryLoading and error from useStoryDetails
  // Handlers
  const handleInputChange = (field: keyof Story, value: any) => {
    setStoryData((prev) => ({ ...prev, [field]: value }));
  };
  const handleContextChange = (
    field: keyof Story["context"],
    value: string
  ) => {
    setStoryData((prev) => ({
      ...prev,
      context: { ...prev.context, [field]: value },
    }));
  };
  const handleCategoryChange = (
    categoryType: keyof Story["categories"],
    values: string[]
  ) => {
    setStoryData((prev) => ({
      ...prev,
      categories: { ...prev.categories, [categoryType]: values },
    }));
  };
  const handleRoleVisibilityChange = (role: string, value: boolean) => {
    setStoryData((prev) => ({
      ...prev,
      roleVisibility: { ...prev.roleVisibility, [role]: value },
    }));
  };
  const handleAddAttachment = (attachment: Attachment) => {
    setStoryData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, attachment],
    }));
  };
  const handleRemoveAttachment = (attachmentId: string) => {
    setStoryData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((att) => att.id !== attachmentId),
    }));
  };
  const handleAccessDetailChange = (field: keyof Credential, value: string) => {
    setNewAccessDetail((prev) => ({ ...prev, [field]: value }));
  };
  const handleSaveAccessDetail = () => {
    if (!newAccessDetail.email || !newAccessDetail.role)
      return toast.error("Email and Role required");
    const newDetail = { ...newAccessDetail, id: nanoid() };
    setAccessDetails((prev) => [...prev, newDetail]);
    setNewAccessDetail(INITIAL_ACCESS_DETAIL);
    setIsAddingAccessDetail(false);
  };
  const handleEditAccessDetail = (detail: Credential) => {
    setNewAccessDetail(detail);
    setIsAddingAccessDetail(true);
  };
  const handleRemoveAccessDetail = (detailId: string) => {
    setAccessDetails((prev) => prev.filter((d) => d.id !== detailId));
  };
  const handleAiContentGenerate = () => {
    setIsProcessingAi(true);
    setTimeout(() => {
      setStoryData((prev) => ({
        ...prev,
        context: {
          ...prev.context,
          solution: "AI-generated solution content",
        },
      }));
      setIsProcessingAi(false);
      toast.success("AI content generated");
    }, 1500);
  };
  const setAiSource = (source: "pdf" | "url" | "text") =>
    setAiState((prev) => ({ ...prev, source }));
  const setSourceUrl = (url: string) =>
    setAiState((prev) => ({ ...prev, sourceUrl: url }));
  const handleAiGenerate = () => {
    toast.info("Generating tags...");
    // Logic here
  };
  const handleFileUpload = (
    type: keyof FileState,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0] || null;
    setFiles((prev) => ({ ...prev, [type]: file }));
  };
  const resetFiles = (type: keyof FileState) => {
    setFiles((prev) => ({ ...prev, [type]: null }));
  };
  const handleAddSuggestedTag = (tagId: string) => {
    const tag = suggestedTags.find((t) => t.id === tagId);
    if (tag) {
      handleCategoryChange(tag.category as keyof Story["categories"], [
        ...storyData.categories[tag.category as keyof Story["categories"]],
        tag.name,
      ]);
    }
  };
  const saveStory = (status: "draft" | "published" | "archived" = "draft") => {
    setIsSaving(true);
    setTimeout(() => {
      setStoryData((prev) => ({ ...prev, status }));
      toast.success(`Story saved as ${status}`);
      setIsSaving(false);
    }, 1500);
  };
  return (
    <StoryContext.Provider
      value={{
        publishedPayload: null,
        setPublishedPayload: () => {},
        storyData,
        isLoading,
        isSaving,
        isEditMode,
        handleInputChange,
        handleContextChange,
        handleCategoryChange,
        handleRoleVisibilityChange,
        handleAddAttachment,
        handleRemoveAttachment,
        saveStory,
        accessDetails,
        newAccessDetail,
        isAddingAccessDetail,
        handleAccessDetailChange,
        handleSaveAccessDetail,
        handleEditAccessDetail,
        handleRemoveAccessDetail,
        setIsAddingAccessDetail,
        aiPrompt,
        setAiPrompt,
        isAiPanelOpen,
        setIsAiPanelOpen,
        handleAiContentGenerate,
        isProcessingAi,
        aiState,
        files,
        setAiSource,
        setSourceUrl,
        handleAiGenerate,
        handleFileUpload,
        resetFiles,
        aiDescription,
        setAiDescription,
        suggestedTags,
        handleAddSuggestedTag,
        documentResponse,
        setDocumentResponse,
        storyResponse,
        setStoryResponse, // Add new state and setter to context
      }}
    >
      {children}
    </StoryContext.Provider>
  );
};
